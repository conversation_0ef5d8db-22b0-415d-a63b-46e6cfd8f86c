'use client';

import { StrictMode } from 'react';
import VPSAdminChat from '@/components/VPSAdminChat';
import ThemeProvider from '@/components/ThemeProvider';

export default function Home() {
  return (
    <StrictMode>
      <ThemeProvider>
        <div className="flex items-center justify-center h-screen lg:min-h-screen bg-theme-secondary p-0 lg:p-4 overflow-hidden lg:overflow-auto transition-all duration-500">
          <VPSAdminChat />
        </div>
      </ThemeProvider>
    </StrictMode>
  );
}
