"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/style-to-js";
exports.ids = ["vendor-chunks/style-to-js"];
exports.modules = {

/***/ "(ssr)/./node_modules/style-to-js/cjs/index.js":
/*!***********************************************!*\
  !*** ./node_modules/style-to-js/cjs/index.js ***!
  \***********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nvar style_to_object_1 = __importDefault(__webpack_require__(/*! style-to-object */ \"(ssr)/./node_modules/style-to-object/cjs/index.js\"));\nvar utilities_1 = __webpack_require__(/*! ./utilities */ \"(ssr)/./node_modules/style-to-js/cjs/utilities.js\");\n/**\n * Parses CSS inline style to JavaScript object (camelCased).\n */\nfunction StyleToJS(style, options) {\n    var output = {};\n    if (!style || typeof style !== 'string') {\n        return output;\n    }\n    (0, style_to_object_1.default)(style, function (property, value) {\n        // skip CSS comment\n        if (property && value) {\n            output[(0, utilities_1.camelCase)(property, options)] = value;\n        }\n    });\n    return output;\n}\nStyleToJS.default = StyleToJS;\nmodule.exports = StyleToJS;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc3R5bGUtdG8tanMvY2pzL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQSw2Q0FBNkM7QUFDN0M7QUFDQSx3Q0FBd0MsbUJBQU8sQ0FBQywwRUFBaUI7QUFDakUsa0JBQWtCLG1CQUFPLENBQUMsc0VBQWE7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxNb2V0ZXpcXERlc2t0b3BcXGdpdGh1Yi13b3JrZmxvd1xcdnBzLWFkbWluLW5leHRqc1xcbm9kZV9tb2R1bGVzXFxzdHlsZS10by1qc1xcY2pzXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfX2ltcG9ydERlZmF1bHQgPSAodGhpcyAmJiB0aGlzLl9faW1wb3J0RGVmYXVsdCkgfHwgZnVuY3Rpb24gKG1vZCkge1xuICAgIHJldHVybiAobW9kICYmIG1vZC5fX2VzTW9kdWxlKSA/IG1vZCA6IHsgXCJkZWZhdWx0XCI6IG1vZCB9O1xufTtcbnZhciBzdHlsZV90b19vYmplY3RfMSA9IF9faW1wb3J0RGVmYXVsdChyZXF1aXJlKFwic3R5bGUtdG8tb2JqZWN0XCIpKTtcbnZhciB1dGlsaXRpZXNfMSA9IHJlcXVpcmUoXCIuL3V0aWxpdGllc1wiKTtcbi8qKlxuICogUGFyc2VzIENTUyBpbmxpbmUgc3R5bGUgdG8gSmF2YVNjcmlwdCBvYmplY3QgKGNhbWVsQ2FzZWQpLlxuICovXG5mdW5jdGlvbiBTdHlsZVRvSlMoc3R5bGUsIG9wdGlvbnMpIHtcbiAgICB2YXIgb3V0cHV0ID0ge307XG4gICAgaWYgKCFzdHlsZSB8fCB0eXBlb2Ygc3R5bGUgIT09ICdzdHJpbmcnKSB7XG4gICAgICAgIHJldHVybiBvdXRwdXQ7XG4gICAgfVxuICAgICgwLCBzdHlsZV90b19vYmplY3RfMS5kZWZhdWx0KShzdHlsZSwgZnVuY3Rpb24gKHByb3BlcnR5LCB2YWx1ZSkge1xuICAgICAgICAvLyBza2lwIENTUyBjb21tZW50XG4gICAgICAgIGlmIChwcm9wZXJ0eSAmJiB2YWx1ZSkge1xuICAgICAgICAgICAgb3V0cHV0WygwLCB1dGlsaXRpZXNfMS5jYW1lbENhc2UpKHByb3BlcnR5LCBvcHRpb25zKV0gPSB2YWx1ZTtcbiAgICAgICAgfVxuICAgIH0pO1xuICAgIHJldHVybiBvdXRwdXQ7XG59XG5TdHlsZVRvSlMuZGVmYXVsdCA9IFN0eWxlVG9KUztcbm1vZHVsZS5leHBvcnRzID0gU3R5bGVUb0pTO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/style-to-js/cjs/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/style-to-js/cjs/utilities.js":
/*!***************************************************!*\
  !*** ./node_modules/style-to-js/cjs/utilities.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.camelCase = void 0;\nvar CUSTOM_PROPERTY_REGEX = /^--[a-zA-Z0-9_-]+$/;\nvar HYPHEN_REGEX = /-([a-z])/g;\nvar NO_HYPHEN_REGEX = /^[^-]+$/;\nvar VENDOR_PREFIX_REGEX = /^-(webkit|moz|ms|o|khtml)-/;\nvar MS_VENDOR_PREFIX_REGEX = /^-(ms)-/;\n/**\n * Checks whether to skip camelCase.\n */\nvar skipCamelCase = function (property) {\n    return !property ||\n        NO_HYPHEN_REGEX.test(property) ||\n        CUSTOM_PROPERTY_REGEX.test(property);\n};\n/**\n * Replacer that capitalizes first character.\n */\nvar capitalize = function (match, character) {\n    return character.toUpperCase();\n};\n/**\n * Replacer that removes beginning hyphen of vendor prefix property.\n */\nvar trimHyphen = function (match, prefix) { return \"\".concat(prefix, \"-\"); };\n/**\n * CamelCases a CSS property.\n */\nvar camelCase = function (property, options) {\n    if (options === void 0) { options = {}; }\n    if (skipCamelCase(property)) {\n        return property;\n    }\n    property = property.toLowerCase();\n    if (options.reactCompat) {\n        // `-ms` vendor prefix should not be capitalized\n        property = property.replace(MS_VENDOR_PREFIX_REGEX, trimHyphen);\n    }\n    else {\n        // for non-React, remove first hyphen so vendor prefix is not capitalized\n        property = property.replace(VENDOR_PREFIX_REGEX, trimHyphen);\n    }\n    return property.replace(HYPHEN_REGEX, capitalize);\n};\nexports.camelCase = camelCase;\n//# sourceMappingURL=utilities.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/style-to-js/cjs/utilities.js\n");

/***/ })

};
;