/**
 * TerminalModal component for displaying terminal output in full view
 * Enhanced with better dark/light mode support and responsive design
 */

import React, { useEffect, useCallback } from 'react';
import { X, Copy, CheckCircle, XCircle, Terminal, Maximize2 } from 'lucide-react';
import { SSHInfo } from '@/types';
import { decodeTerminalOutput } from '@/utils/terminalUtils';
import { useAppStore } from '@/store/appStore';

interface TerminalModalProps {
  isOpen: boolean;
  onClose: () => void;
  sshInfo: SSHInfo;
}

const TerminalModal: React.FC<TerminalModalProps> = ({ isOpen, onClose, sshInfo }) => {
  const [copiedSection, setCopiedSection] = React.useState<string | null>(null);
  const [isFullscreen, setIsFullscreen] = React.useState(false);
  const { theme } = useAppStore();

  // Enhanced keyboard navigation
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      onClose();
    } else if (event.key === 'F11') {
      event.preventDefault();
      setIsFullscreen(!isFullscreen);
    }
  }, [onClose, isFullscreen]);

  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden'; // Prevent background scroll
      return () => {
        document.removeEventListener('keydown', handleKeyDown);
        document.body.style.overflow = 'unset';
      };
    }
  }, [isOpen, handleKeyDown]);

  // Enhanced terminal styles using CSS custom properties
  const getTerminalStyles = () => ({
    backgroundColor: 'rgb(var(--terminal-bg))',
    color: 'rgb(var(--terminal-text))',
    fontFamily: 'Consolas, Monaco, "Courier New", monospace',
    fontSize: '0.875rem',
    lineHeight: '1.3',
    whiteSpace: 'pre' as const,
    overflowX: 'auto' as const,
    border: 'none',
    outline: 'none',
    tabSize: 8,
    fontFeatureSettings: '"liga" 0, "calt" 0',
    fontVariantLigatures: 'none'
  });

  // Enhanced copy to clipboard functionality with better feedback
  const copyToClipboard = async (text: string, section: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedSection(section);
      setTimeout(() => setCopiedSection(null), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      try {
        document.execCommand('copy');
        setCopiedSection(section);
        setTimeout(() => setCopiedSection(null), 2000);
      } catch (fallbackErr) {
        console.error('Fallback copy failed: ', fallbackErr);
      }
      document.body.removeChild(textArea);
    }
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  if (!isOpen) return null;

  const modalSizeClasses = isFullscreen
    ? "fixed inset-0 w-full h-full max-w-none max-h-none rounded-none"
    : "relative w-full max-w-7xl max-h-[95vh] sm:max-h-[90vh] rounded-none sm:rounded-xl";

  const contentHeightClasses = isFullscreen
    ? "max-h-[calc(100vh-120px)]"
    : "max-h-[calc(95vh-120px)] sm:max-h-[calc(90vh-140px)]";

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-0 sm:p-4 bg-black/60 backdrop-blur-sm animate-fade-in">
      {/* Modal Container */}
      <div className={`${modalSizeClasses} bg-surface-primary border border-theme-primary shadow-2xl overflow-hidden animate-scale-in`}>
        {/* Header */}
        <div className="flex items-center justify-between p-3 sm:p-4 border-b border-theme-primary bg-surface-secondary">
          <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
            <Terminal size={18} className="text-accent-primary flex-shrink-0" />
            {sshInfo?.success ? (
              <CheckCircle size={16} className="text-green-500 flex-shrink-0" />
            ) : (
              <XCircle size={16} className="text-red-500 flex-shrink-0" />
            )}
            <h2 className="text-base sm:text-lg font-semibold text-theme-primary truncate">
              Terminal Output - Command {sshInfo?.success ? 'Succeeded' : 'Failed'}
            </h2>
            <span className="hidden sm:inline text-sm text-theme-secondary bg-surface-tertiary px-2 py-1 rounded flex-shrink-0">
              Exit Code: {sshInfo?.exit_status ?? 'N/A'}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={toggleFullscreen}
              className="p-2 hover:bg-surface-tertiary rounded-lg transition-colors text-theme-secondary hover:text-theme-primary flex-shrink-0"
              aria-label={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
              title={isFullscreen ? "Exit fullscreen (F11)" : "Enter fullscreen (F11)"}
            >
              <Maximize2 size={16} />
            </button>
            <button
              onClick={onClose}
              className="p-2 hover:bg-surface-tertiary rounded-lg transition-colors text-theme-secondary hover:text-theme-primary flex-shrink-0"
              aria-label="Close modal"
              title="Close (Esc)"
            >
              <X size={18} />
            </button>
          </div>
        </div>

        {/* Mobile Exit Code */}
        <div className="sm:hidden px-3 py-2 border-b border-theme-primary bg-surface-secondary">
          <span className="text-sm text-theme-secondary">
            Exit Code: {sshInfo?.exit_status ?? 'N/A'}
          </span>
        </div>

        {/* Content */}
        <div className={`flex-1 overflow-auto ${contentHeightClasses}`}>
          <div className="p-3 sm:p-4 space-y-4">
            {/* Command Info */}
            {sshInfo?.command && (
              <div className="bg-surface-secondary border border-theme-primary rounded-lg overflow-hidden">
                <div className="flex items-center justify-between p-3 bg-surface-tertiary border-b border-theme-primary">
                  <div className="flex items-center gap-2">
                    <Terminal size={14} className="text-accent-primary" />
                    <span className="text-sm font-medium text-theme-primary">Command</span>
                  </div>
                  <button
                    onClick={() => copyToClipboard(decodeTerminalOutput(sshInfo.command!), 'command')}
                    className="flex items-center gap-1 text-xs text-theme-secondary hover:text-theme-primary transition-colors px-2 py-1 hover:bg-surface-primary rounded"
                    title="Copy command"
                  >
                    {copiedSection === 'command' ? <CheckCircle size={12} /> : <Copy size={12} />}
                    <span className="hidden sm:inline">{copiedSection === 'command' ? 'Copied!' : 'Copy'}</span>
                  </button>
                </div>
                <div className="p-4" style={getTerminalStyles()}>
                  <pre
                    className="terminal-command terminal-modal-output text-sm overflow-x-auto"
                    style={getTerminalStyles()}
                  >
                    {decodeTerminalOutput(sshInfo.command)}
                  </pre>
                </div>
              </div>
            )}

            {/* Execution Time */}
            {sshInfo?.execution_time && (
              <div className="flex items-center gap-2 text-sm text-theme-secondary bg-surface-secondary px-3 py-2 rounded-lg border border-theme-primary">
                <span className="text-accent-primary">⏱</span>
                <span>Execution time: {sshInfo.execution_time}ms</span>
              </div>
            )}

            {/* Stdout Section */}
            {sshInfo?.stdout && (
              <div className="bg-surface-secondary border border-theme-primary rounded-lg overflow-hidden">
                <div className="flex items-center justify-between p-3 bg-terminal-success-bg border-b border-terminal-success-border">
                  <div className="flex items-center gap-2">
                    <CheckCircle size={16} className="text-terminal-success-text" />
                    <span className="font-medium text-terminal-success-text">Standard Output</span>
                  </div>
                  <button
                    onClick={() => copyToClipboard(decodeTerminalOutput(sshInfo.stdout), 'stdout')}
                    className="flex items-center gap-1 text-xs text-terminal-success-text hover:opacity-80 transition-opacity px-2 py-1 hover:bg-terminal-success-bg/50 rounded"
                    title="Copy stdout"
                  >
                    {copiedSection === 'stdout' ? <CheckCircle size={12} /> : <Copy size={12} />}
                    <span className="hidden sm:inline">{copiedSection === 'stdout' ? 'Copied!' : 'Copy'}</span>
                  </button>
                </div>
                <div className="p-4" style={getTerminalStyles()}>
                  <pre
                    className="terminal-pre terminal-stdout terminal-modal-output"
                    style={getTerminalStyles()}
                  >
                    {decodeTerminalOutput(sshInfo.stdout)}
                  </pre>
                </div>
              </div>
            )}

            {/* Stderr Section */}
            {sshInfo?.stderr && (
              <div className="bg-surface-secondary border border-theme-primary rounded-lg overflow-hidden">
                <div className="flex items-center justify-between p-3 bg-terminal-error-bg border-b border-terminal-error-border">
                  <div className="flex items-center gap-2">
                    <XCircle size={16} className="text-terminal-error-text" />
                    <span className="font-medium text-terminal-error-text">Standard Error</span>
                  </div>
                  <button
                    onClick={() => copyToClipboard(decodeTerminalOutput(sshInfo.stderr), 'stderr')}
                    className="flex items-center gap-1 text-xs text-terminal-error-text hover:opacity-80 transition-opacity px-2 py-1 hover:bg-terminal-error-bg/50 rounded"
                    title="Copy stderr"
                  >
                    {copiedSection === 'stderr' ? <CheckCircle size={12} /> : <Copy size={12} />}
                    <span className="hidden sm:inline">{copiedSection === 'stderr' ? 'Copied!' : 'Copy'}</span>
                  </button>
                </div>
                <div className="p-4" style={getTerminalStyles()}>
                  <pre
                    className="terminal-pre terminal-stderr terminal-modal-output"
                    style={getTerminalStyles()}
                  >
                    {decodeTerminalOutput(sshInfo.stderr)}
                  </pre>
                </div>
              </div>
            )}

            {/* Empty output message */}
            {!sshInfo?.stdout && !sshInfo?.stderr && (
              <div className="text-center py-12 text-theme-secondary">
                <Terminal size={48} className="mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium mb-2">No output available</p>
                <p className="text-sm opacity-75">The command executed but produced no output</p>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-3 sm:p-4 border-t border-theme-primary bg-surface-secondary">
          <div className="flex items-center gap-4 text-sm text-theme-secondary">
            <div className="hidden sm:flex items-center gap-2">
              <kbd className="px-2 py-1 bg-surface-primary border border-theme-primary rounded text-xs font-mono">Esc</kbd>
              <span>Close</span>
            </div>
            <div className="hidden sm:flex items-center gap-2">
              <kbd className="px-2 py-1 bg-surface-primary border border-theme-primary rounded text-xs font-mono">F11</kbd>
              <span>Fullscreen</span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={toggleFullscreen}
              className="sm:hidden px-3 py-2 bg-surface-tertiary text-theme-primary rounded-lg hover:bg-interactive-hover transition-colors text-sm"
            >
              {isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
            </button>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-accent-primary text-white rounded-lg hover:opacity-90 transition-all transform hover:scale-105 font-medium"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TerminalModal;
