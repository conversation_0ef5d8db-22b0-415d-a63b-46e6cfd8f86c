"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/remark-rehype";
exports.ids = ["vendor-chunks/remark-rehype"];
exports.modules = {

/***/ "(ssr)/./node_modules/remark-rehype/lib/index.js":
/*!*************************************************!*\
  !*** ./node_modules/remark-rehype/lib/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ remarkRehype)\n/* harmony export */ });\n/* harmony import */ var mdast_util_to_hast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-to-hast */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/index.js\");\n/**\n * @import {Root as HastRoot} from 'hast'\n * @import {Root as MdastRoot} from 'mdast'\n * @import {Options as ToHastOptions} from 'mdast-util-to-hast'\n * @import {Processor} from 'unified'\n * @import {VFile} from 'vfile'\n */\n\n/**\n * @typedef {Omit<ToHastOptions, 'file'>} Options\n *\n * @callback TransformBridge\n *   Bridge-mode.\n *\n *   Runs the destination with the new hast tree.\n *   Discards result.\n * @param {MdastRoot} tree\n *   Tree.\n * @param {VFile} file\n *   File.\n * @returns {Promise<undefined>}\n *   Nothing.\n *\n * @callback TransformMutate\n *  Mutate-mode.\n *\n *  Further transformers run on the hast tree.\n * @param {MdastRoot} tree\n *   Tree.\n * @param {VFile} file\n *   File.\n * @returns {HastRoot}\n *   Tree (hast).\n */\n\n\n\n/**\n * Turn markdown into HTML.\n *\n * ##### Notes\n *\n * ###### Signature\n *\n * * if a processor is given,\n *   runs the (rehype) plugins used on it with a hast tree,\n *   then discards the result (*bridge mode*)\n * * otherwise,\n *   returns a hast tree,\n *   the plugins used after `remarkRehype` are rehype plugins (*mutate mode*)\n *\n * > 👉 **Note**:\n * > It’s highly unlikely that you want to pass a `processor`.\n *\n * ###### HTML\n *\n * Raw HTML is available in mdast as `html` nodes and can be embedded in hast\n * as semistandard `raw` nodes.\n * Most plugins ignore `raw` nodes but two notable ones don’t:\n *\n * * `rehype-stringify` also has an option `allowDangerousHtml` which will\n *   output the raw HTML.\n *   This is typically discouraged as noted by the option name but is useful if\n *   you completely trust authors\n * * `rehype-raw` can handle the raw embedded HTML strings by parsing them\n *   into standard hast nodes (`element`, `text`, etc);\n *   this is a heavy task as it needs a full HTML parser,\n *   but it is the only way to support untrusted content\n *\n * ###### Footnotes\n *\n * Many options supported here relate to footnotes.\n * Footnotes are not specified by CommonMark,\n * which we follow by default.\n * They are supported by GitHub,\n * so footnotes can be enabled in markdown with `remark-gfm`.\n *\n * The options `footnoteBackLabel` and `footnoteLabel` define natural language\n * that explains footnotes,\n * which is hidden for sighted users but shown to assistive technology.\n * When your page is not in English,\n * you must define translated values.\n *\n * Back references use ARIA attributes,\n * but the section label itself uses a heading that is hidden with an\n * `sr-only` class.\n * To show it to sighted users,\n * define different attributes in `footnoteLabelProperties`.\n *\n * ###### Clobbering\n *\n * Footnotes introduces a problem,\n * as it links footnote calls to footnote definitions on the page through `id`\n * attributes generated from user content,\n * which results in DOM clobbering.\n *\n * DOM clobbering is this:\n *\n * ```html\n * <p id=x></p>\n * <script>alert(x) // `x` now refers to the DOM `p#x` element</script>\n * ```\n *\n * Elements by their ID are made available by browsers on the `window` object,\n * which is a security risk.\n * Using a prefix solves this problem.\n *\n * More information on how to handle clobbering and the prefix is explained in\n * *Example: headings (DOM clobbering)* in `rehype-sanitize`.\n *\n * ###### Unknown nodes\n *\n * Unknown nodes are nodes with a type that isn’t in `handlers` or `passThrough`.\n * The default behavior for unknown nodes is:\n *\n * * when the node has a `value`\n *   (and doesn’t have `data.hName`, `data.hProperties`, or `data.hChildren`,\n *   see later),\n *   create a hast `text` node\n * * otherwise,\n *   create a `<div>` element (which could be changed with `data.hName`),\n *   with its children mapped from mdast to hast as well\n *\n * This behavior can be changed by passing an `unknownHandler`.\n *\n * @overload\n * @param {Processor} processor\n * @param {Readonly<Options> | null | undefined} [options]\n * @returns {TransformBridge}\n *\n * @overload\n * @param {Readonly<Options> | null | undefined} [options]\n * @returns {TransformMutate}\n *\n * @overload\n * @param {Readonly<Options> | Processor | null | undefined} [destination]\n * @param {Readonly<Options> | null | undefined} [options]\n * @returns {TransformBridge | TransformMutate}\n *\n * @param {Readonly<Options> | Processor | null | undefined} [destination]\n *   Processor or configuration (optional).\n * @param {Readonly<Options> | null | undefined} [options]\n *   When a processor was given,\n *   configuration (optional).\n * @returns {TransformBridge | TransformMutate}\n *   Transform.\n */\nfunction remarkRehype(destination, options) {\n  if (destination && 'run' in destination) {\n    /**\n     * @type {TransformBridge}\n     */\n    return async function (tree, file) {\n      // Cast because root in -> root out.\n      const hastTree = /** @type {HastRoot} */ (\n        (0,mdast_util_to_hast__WEBPACK_IMPORTED_MODULE_0__.toHast)(tree, {file, ...options})\n      )\n      await destination.run(hastTree, file)\n    }\n  }\n\n  /**\n   * @type {TransformMutate}\n   */\n  return function (tree, file) {\n    // Cast because root in -> root out.\n    // To do: in the future, disallow ` || options` fallback.\n    // With `unified-engine`, `destination` can be `undefined` but\n    // `options` will be the file set.\n    // We should not pass that as `options`.\n    return /** @type {HastRoot} */ (\n      (0,mdast_util_to_hast__WEBPACK_IMPORTED_MODULE_0__.toHast)(tree, {file, ...(destination || options)})\n    )\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/remark-rehype/lib/index.js\n");

/***/ })

};
;