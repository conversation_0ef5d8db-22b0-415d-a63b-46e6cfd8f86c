/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
/*! tailwindcss v4.1.8 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    --color-red-50: oklch(97.1% 0.013 17.38);
    --color-red-100: oklch(93.6% 0.032 17.717);
    --color-red-300: oklch(80.8% 0.114 19.571);
    --color-red-400: oklch(70.4% 0.191 22.216);
    --color-red-500: oklch(63.7% 0.237 25.331);
    --color-red-600: oklch(57.7% 0.245 27.325);
    --color-red-700: oklch(50.5% 0.213 27.518);
    --color-red-800: oklch(44.4% 0.177 26.899);
    --color-red-900: oklch(39.6% 0.141 25.723);
    --color-orange-100: oklch(95.4% 0.038 75.164);
    --color-orange-300: oklch(83.7% 0.128 66.29);
    --color-orange-400: oklch(75% 0.183 55.934);
    --color-orange-500: oklch(70.5% 0.213 47.604);
    --color-orange-600: oklch(64.6% 0.222 41.116);
    --color-orange-700: oklch(55.3% 0.195 38.402);
    --color-orange-900: oklch(40.8% 0.123 38.172);
    --color-amber-400: oklch(82.8% 0.189 84.429);
    --color-amber-500: oklch(76.9% 0.188 70.08);
    --color-amber-600: oklch(66.6% 0.179 58.318);
    --color-yellow-100: oklch(97.3% 0.071 103.193);
    --color-yellow-200: oklch(94.5% 0.129 101.54);
    --color-yellow-300: oklch(90.5% 0.182 98.111);
    --color-yellow-400: oklch(85.2% 0.199 91.936);
    --color-yellow-500: oklch(79.5% 0.184 86.047);
    --color-yellow-600: oklch(68.1% 0.162 75.834);
    --color-yellow-700: oklch(55.4% 0.135 66.442);
    --color-yellow-900: oklch(42.1% 0.095 57.708);
    --color-green-100: oklch(96.2% 0.044 156.743);
    --color-green-200: oklch(92.5% 0.084 155.995);
    --color-green-400: oklch(79.2% 0.209 151.711);
    --color-green-500: oklch(72.3% 0.219 149.579);
    --color-green-600: oklch(62.7% 0.194 149.214);
    --color-green-800: oklch(44.8% 0.119 151.328);
    --color-emerald-400: oklch(76.5% 0.177 163.223);
    --color-emerald-500: oklch(69.6% 0.17 162.48);
    --color-emerald-600: oklch(59.6% 0.145 163.225);
    --color-teal-100: oklch(95.3% 0.051 180.801);
    --color-teal-200: oklch(91% 0.096 180.426);
    --color-teal-300: oklch(85.5% 0.138 181.071);
    --color-teal-400: oklch(77.7% 0.152 181.912);
    --color-teal-500: oklch(70.4% 0.14 182.503);
    --color-teal-600: oklch(60% 0.118 184.704);
    --color-sky-100: oklch(95.1% 0.026 236.824);
    --color-sky-200: oklch(90.1% 0.058 230.902);
    --color-sky-300: oklch(82.8% 0.111 230.318);
    --color-sky-400: oklch(74.6% 0.16 232.661);
    --color-sky-500: oklch(68.5% 0.169 237.323);
    --color-sky-600: oklch(58.8% 0.158 241.966);
    --color-blue-100: oklch(93.2% 0.032 255.585);
    --color-blue-200: oklch(88.2% 0.059 254.128);
    --color-blue-400: oklch(70.7% 0.165 254.624);
    --color-blue-500: oklch(62.3% 0.214 259.815);
    --color-blue-600: oklch(54.6% 0.245 262.881);
    --color-blue-700: oklch(48.8% 0.243 264.376);
    --color-blue-800: oklch(42.4% 0.199 265.638);
    --color-indigo-400: oklch(67.3% 0.182 276.935);
    --color-indigo-500: oklch(58.5% 0.233 277.117);
    --color-purple-100: oklch(94.6% 0.033 307.174);
    --color-purple-200: oklch(90.2% 0.063 306.703);
    --color-purple-400: oklch(71.4% 0.203 305.504);
    --color-purple-500: oklch(62.7% 0.265 303.9);
    --color-purple-700: oklch(49.6% 0.265 301.924);
    --color-slate-200: oklch(92.9% 0.013 255.508);
    --color-slate-300: oklch(86.9% 0.022 252.894);
    --color-slate-400: oklch(70.4% 0.04 256.788);
    --color-slate-500: oklch(55.4% 0.046 257.417);
    --color-slate-600: oklch(44.6% 0.043 257.281);
    --color-slate-700: oklch(37.2% 0.044 257.287);
    --color-slate-800: oklch(27.9% 0.041 260.031);
    --color-gray-100: oklch(96.7% 0.003 264.542);
    --color-gray-300: oklch(87.2% 0.01 258.338);
    --color-gray-400: oklch(70.7% 0.022 261.325);
    --color-gray-500: oklch(55.1% 0.027 264.364);
    --color-gray-600: oklch(44.6% 0.03 256.802);
    --color-gray-700: oklch(37.3% 0.034 259.733);
    --color-gray-800: oklch(27.8% 0.033 256.848);
    --color-black: #000;
    --color-white: #fff;
    --spacing: 0.25rem;
    --container-xs: 20rem;
    --container-4xl: 56rem;
    --container-7xl: 80rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --leading-relaxed: 1.625;
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --animate-spin: spin 1s linear infinite;
    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    --animate-bounce: bounce 1s infinite;
    --blur-sm: 8px;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
    --color-primary: 20 184 166;
    --color-primary-dark: 15 118 110;
    --color-success: 34 197 94;
    --color-error: 239 68 68;
    --duration-fast: 150ms;
    --duration-normal: 250ms;
    --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .top-1\/2 {
    top: calc(1/2 * 100%);
  }
  .top-2 {
    top: calc(var(--spacing) * 2);
  }
  .-right-1 {
    right: calc(var(--spacing) * -1);
  }
  .right-0 {
    right: calc(var(--spacing) * 0);
  }
  .right-2 {
    right: calc(var(--spacing) * 2);
  }
  .right-3 {
    right: calc(var(--spacing) * 3);
  }
  .right-4 {
    right: calc(var(--spacing) * 4);
  }
  .-bottom-1 {
    bottom: calc(var(--spacing) * -1);
  }
  .bottom-4 {
    bottom: calc(var(--spacing) * 4);
  }
  .bottom-full {
    bottom: 100%;
  }
  .left-0 {
    left: calc(var(--spacing) * 0);
  }
  .z-10 {
    z-index: 10;
  }
  .z-50 {
    z-index: 50;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .mx-auto {
    margin-inline: auto;
  }
  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }
  .mr-auto {
    margin-right: auto;
  }
  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }
  .mb-1\.5 {
    margin-bottom: calc(var(--spacing) * 1.5);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }
  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }
  .ml-auto {
    margin-left: auto;
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline {
    display: inline;
  }
  .inline-block {
    display: inline-block;
  }
  .h-1\.5 {
    height: calc(var(--spacing) * 1.5);
  }
  .h-2 {
    height: calc(var(--spacing) * 2);
  }
  .h-3 {
    height: calc(var(--spacing) * 3);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-full {
    height: 100%;
  }
  .h-screen {
    height: 100vh;
  }
  .max-h-20 {
    max-height: calc(var(--spacing) * 20);
  }
  .max-h-40 {
    max-height: calc(var(--spacing) * 40);
  }
  .max-h-64 {
    max-height: calc(var(--spacing) * 64);
  }
  .max-h-96 {
    max-height: calc(var(--spacing) * 96);
  }
  .max-h-\[80vh\] {
    max-height: 80vh;
  }
  .max-h-\[95vh\] {
    max-height: 95vh;
  }
  .max-h-\[calc\(95vh-120px\)\] {
    max-height: calc(95vh - 120px);
  }
  .max-h-\[calc\(100vh-120px\)\] {
    max-height: calc(100vh - 120px);
  }
  .max-h-none {
    max-height: none;
  }
  .min-h-0 {
    min-height: calc(var(--spacing) * 0);
  }
  .w-1\.5 {
    width: calc(var(--spacing) * 1.5);
  }
  .w-2 {
    width: calc(var(--spacing) * 2);
  }
  .w-3 {
    width: calc(var(--spacing) * 3);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-full {
    width: 100%;
  }
  .max-w-4xl {
    max-width: var(--container-4xl);
  }
  .max-w-7xl {
    max-width: var(--container-7xl);
  }
  .max-w-\[70\%\] {
    max-width: 70%;
  }
  .max-w-\[75\%\] {
    max-width: 75%;
  }
  .max-w-full {
    max-width: 100%;
  }
  .max-w-none {
    max-width: none;
  }
  .max-w-xs {
    max-width: var(--container-xs);
  }
  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }
  .flex-1 {
    flex: 1;
  }
  .flex-shrink-0 {
    flex-shrink: 0;
  }
  .translate-x-0 {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-x-full {
    --tw-translate-x: 100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .animate-bounce {
    animation: var(--animate-bounce);
  }
  .animate-pulse {
    animation: var(--animate-pulse);
  }
  .animate-spin {
    animation: var(--animate-spin);
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .touch-manipulation {
    touch-action: manipulation;
  }
  .list-inside {
    list-style-position: inside;
  }
  .list-decimal {
    list-style-type: decimal;
  }
  .list-disc {
    list-style-type: disc;
  }
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .flex-col {
    flex-direction: column;
  }
  .items-center {
    align-items: center;
  }
  .items-end {
    align-items: flex-end;
  }
  .items-start {
    align-items: flex-start;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .gap-0 {
    gap: calc(var(--spacing) * 0);
  }
  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }
  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .space-y-0 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-1 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-2 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-3 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-4 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-x-1 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-3 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .self-center {
    align-self: center;
  }
  .self-end {
    align-self: flex-end;
  }
  .self-start {
    align-self: flex-start;
  }
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .overflow-auto {
    overflow: auto;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .overflow-x-auto {
    overflow-x: auto;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-md {
    border-radius: var(--radius-md);
  }
  .rounded-none {
    border-radius: 0;
  }
  .rounded-sm {
    border-radius: var(--radius-sm);
  }
  .rounded-xl {
    border-radius: var(--radius-xl);
  }
  .rounded-tl-none {
    border-top-left-radius: 0;
  }
  .rounded-tr-none {
    border-top-right-radius: 0;
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-0 {
    border-style: var(--tw-border-style);
    border-width: 0px;
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-l-2 {
    border-left-style: var(--tw-border-style);
    border-left-width: 2px;
  }
  .border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
  }
  .border-blue-400 {
    border-color: var(--color-blue-400);
  }
  .border-current\/30 {
    border-color: currentcolor;
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, currentcolor 30%, transparent);
    }
  }
  .border-gray-300 {
    border-color: var(--color-gray-300);
  }
  .border-gray-400 {
    border-color: var(--color-gray-400);
  }
  .border-green-400 {
    border-color: var(--color-green-400);
  }
  .border-indigo-400 {
    border-color: var(--color-indigo-400);
  }
  .border-orange-300 {
    border-color: var(--color-orange-300);
  }
  .border-purple-400 {
    border-color: var(--color-purple-400);
  }
  .border-red-300 {
    border-color: var(--color-red-300);
  }
  .border-red-400 {
    border-color: var(--color-red-400);
  }
  .border-red-500 {
    border-color: var(--color-red-500);
  }
  .border-sky-200 {
    border-color: var(--color-sky-200);
  }
  .border-slate-300 {
    border-color: var(--color-slate-300);
  }
  .border-teal-200 {
    border-color: var(--color-teal-200);
  }
  .border-teal-400 {
    border-color: var(--color-teal-400);
  }
  .border-white\/10 {
    border-color: color-mix(in srgb, #fff 10%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-white) 10%, transparent);
    }
  }
  .border-white\/20 {
    border-color: color-mix(in srgb, #fff 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      border-color: color-mix(in oklab, var(--color-white) 20%, transparent);
    }
  }
  .border-yellow-300 {
    border-color: var(--color-yellow-300);
  }
  .border-yellow-400 {
    border-color: var(--color-yellow-400);
  }
  .bg-amber-500 {
    background-color: var(--color-amber-500);
  }
  .bg-black {
    background-color: var(--color-black);
  }
  .bg-black\/60 {
    background-color: color-mix(in srgb, #000 60%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-black) 60%, transparent);
    }
  }
  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }
  .bg-blue-500 {
    background-color: var(--color-blue-500);
  }
  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }
  .bg-emerald-500 {
    background-color: var(--color-emerald-500);
  }
  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }
  .bg-gray-300 {
    background-color: var(--color-gray-300);
  }
  .bg-gray-400 {
    background-color: var(--color-gray-400);
  }
  .bg-gray-800 {
    background-color: var(--color-gray-800);
  }
  .bg-green-100 {
    background-color: var(--color-green-100);
  }
  .bg-green-500 {
    background-color: var(--color-green-500);
  }
  .bg-orange-100 {
    background-color: var(--color-orange-100);
  }
  .bg-orange-600 {
    background-color: var(--color-orange-600);
  }
  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }
  .bg-red-50 {
    background-color: var(--color-red-50);
  }
  .bg-red-100 {
    background-color: var(--color-red-100);
  }
  .bg-red-500 {
    background-color: var(--color-red-500);
  }
  .bg-red-700 {
    background-color: var(--color-red-700);
  }
  .bg-sky-100 {
    background-color: var(--color-sky-100);
  }
  .bg-sky-500 {
    background-color: var(--color-sky-500);
  }
  .bg-slate-200 {
    background-color: var(--color-slate-200);
  }
  .bg-slate-600 {
    background-color: var(--color-slate-600);
  }
  .bg-teal-100 {
    background-color: var(--color-teal-100);
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .bg-yellow-100 {
    background-color: var(--color-yellow-100);
  }
  .bg-yellow-500\/20 {
    background-color: color-mix(in srgb, oklch(79.5% 0.184 86.047) 20%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-yellow-500) 20%, transparent);
    }
  }
  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }
  .from-orange-500 {
    --tw-gradient-from: var(--color-orange-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .from-red-500 {
    --tw-gradient-from: var(--color-red-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-orange-600 {
    --tw-gradient-to: var(--color-orange-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .to-red-600 {
    --tw-gradient-to: var(--color-red-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }
  .p-0 {
    padding: calc(var(--spacing) * 0);
  }
  .p-1 {
    padding: calc(var(--spacing) * 1);
  }
  .p-1\.5 {
    padding: calc(var(--spacing) * 1.5);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }
  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .py-0\.5 {
    padding-block: calc(var(--spacing) * 0.5);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }
  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }
  .pt-3 {
    padding-top: calc(var(--spacing) * 3);
  }
  .pb-3 {
    padding-bottom: calc(var(--spacing) * 3);
  }
  .pl-2 {
    padding-left: calc(var(--spacing) * 2);
  }
  .pl-3 {
    padding-left: calc(var(--spacing) * 3);
  }
  .text-center {
    text-align: center;
  }
  .text-left {
    text-align: left;
  }
  .font-mono {
    font-family: var(--font-mono);
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .break-words {
    overflow-wrap: break-word;
  }
  .text-blue-500 {
    color: var(--color-blue-500);
  }
  .text-blue-600 {
    color: var(--color-blue-600);
  }
  .text-blue-700 {
    color: var(--color-blue-700);
  }
  .text-gray-400 {
    color: var(--color-gray-400);
  }
  .text-gray-500 {
    color: var(--color-gray-500);
  }
  .text-gray-700 {
    color: var(--color-gray-700);
  }
  .text-green-500 {
    color: var(--color-green-500);
  }
  .text-green-600 {
    color: var(--color-green-600);
  }
  .text-green-800 {
    color: var(--color-green-800);
  }
  .text-indigo-500 {
    color: var(--color-indigo-500);
  }
  .text-orange-700 {
    color: var(--color-orange-700);
  }
  .text-purple-500 {
    color: var(--color-purple-500);
  }
  .text-purple-700 {
    color: var(--color-purple-700);
  }
  .text-red-500 {
    color: var(--color-red-500);
  }
  .text-red-600 {
    color: var(--color-red-600);
  }
  .text-red-700 {
    color: var(--color-red-700);
  }
  .text-sky-500 {
    color: var(--color-sky-500);
  }
  .text-sky-600 {
    color: var(--color-sky-600);
  }
  .text-slate-600 {
    color: var(--color-slate-600);
  }
  .text-slate-700 {
    color: var(--color-slate-700);
  }
  .text-slate-800 {
    color: var(--color-slate-800);
  }
  .text-teal-500 {
    color: var(--color-teal-500);
  }
  .text-teal-600 {
    color: var(--color-teal-600);
  }
  .text-white {
    color: var(--color-white);
  }
  .text-yellow-200 {
    color: var(--color-yellow-200);
  }
  .text-yellow-500 {
    color: var(--color-yellow-500);
  }
  .text-yellow-600 {
    color: var(--color-yellow-600);
  }
  .text-yellow-700 {
    color: var(--color-yellow-700);
  }
  .italic {
    font-style: italic;
  }
  .ordinal {
    --tw-ordinal: ordinal;
    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);
  }
  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .opacity-50 {
    opacity: 50%;
  }
  .opacity-70 {
    opacity: 70%;
  }
  .opacity-75 {
    opacity: 75%;
  }
  .opacity-100 {
    opacity: 100%;
  }
  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-none {
    --tw-shadow: 0 0 #0000;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-1 {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-green-200 {
    --tw-ring-color: var(--color-green-200);
  }
  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);
  }
  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .duration-200 {
    --tw-duration: 200ms;
    transition-duration: 200ms;
  }
  .duration-300 {
    --tw-duration: 300ms;
    transition-duration: 300ms;
  }
  .duration-500 {
    --tw-duration: 500ms;
    transition-duration: 500ms;
  }
  .last\:mb-0 {
    &:last-child {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }
  .last\:border-b-0 {
    &:last-child {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 0px;
    }
  }
  .hover\:scale-105 {
    &:hover {
      @media (hover: hover) {
        --tw-scale-x: 105%;
        --tw-scale-y: 105%;
        --tw-scale-z: 105%;
        scale: var(--tw-scale-x) var(--tw-scale-y);
      }
    }
  }
  .hover\:transform {
    &:hover {
      @media (hover: hover) {
        transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
      }
    }
  }
  .hover\:bg-amber-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-amber-600);
      }
    }
  }
  .hover\:bg-blue-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-200);
      }
    }
  }
  .hover\:bg-blue-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-700);
      }
    }
  }
  .hover\:bg-emerald-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-emerald-600);
      }
    }
  }
  .hover\:bg-gray-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-700);
      }
    }
  }
  .hover\:bg-orange-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-orange-700);
      }
    }
  }
  .hover\:bg-purple-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-purple-200);
      }
    }
  }
  .hover\:bg-red-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-600);
      }
    }
  }
  .hover\:bg-red-800 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-800);
      }
    }
  }
  .hover\:bg-sky-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-sky-600);
      }
    }
  }
  .hover\:bg-slate-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-slate-700);
      }
    }
  }
  .hover\:text-blue-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-800);
      }
    }
  }
  .hover\:text-gray-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-700);
      }
    }
  }
  .hover\:text-red-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-red-800);
      }
    }
  }
  .hover\:opacity-80 {
    &:hover {
      @media (hover: hover) {
        opacity: 80%;
      }
    }
  }
  .hover\:opacity-90 {
    &:hover {
      @media (hover: hover) {
        opacity: 90%;
      }
    }
  }
  .hover\:opacity-100 {
    &:hover {
      @media (hover: hover) {
        opacity: 100%;
      }
    }
  }
  .hover\:shadow-lg {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:shadow-md {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .hover\:shadow-xl {
    &:hover {
      @media (hover: hover) {
        --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      }
    }
  }
  .focus\:shadow-lg {
    &:focus {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-2 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-amber-400 {
    &:focus {
      --tw-ring-color: var(--color-amber-400);
    }
  }
  .focus\:ring-emerald-400 {
    &:focus {
      --tw-ring-color: var(--color-emerald-400);
    }
  }
  .focus\:ring-red-400 {
    &:focus {
      --tw-ring-color: var(--color-red-400);
    }
  }
  .focus\:ring-red-600 {
    &:focus {
      --tw-ring-color: var(--color-red-600);
    }
  }
  .focus\:ring-sky-400 {
    &:focus {
      --tw-ring-color: var(--color-sky-400);
    }
  }
  .focus\:ring-slate-400 {
    &:focus {
      --tw-ring-color: var(--color-slate-400);
    }
  }
  .focus\:ring-offset-2 {
    &:focus {
      --tw-ring-offset-width: 2px;
      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    }
  }
  .focus\:outline-none {
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .active\:scale-95 {
    &:active {
      --tw-scale-x: 95%;
      --tw-scale-y: 95%;
      --tw-scale-z: 95%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
  .disabled\:transform-none {
    &:disabled {
      transform: none;
    }
  }
  .disabled\:cursor-not-allowed {
    &:disabled {
      cursor: not-allowed;
    }
  }
  .disabled\:opacity-50 {
    &:disabled {
      opacity: 50%;
    }
  }
  .sm\:flex {
    @media (width >= 40rem) {
      display: flex;
    }
  }
  .sm\:hidden {
    @media (width >= 40rem) {
      display: none;
    }
  }
  .sm\:inline {
    @media (width >= 40rem) {
      display: inline;
    }
  }
  .sm\:max-h-\[90vh\] {
    @media (width >= 40rem) {
      max-height: 90vh;
    }
  }
  .sm\:max-h-\[calc\(90vh-140px\)\] {
    @media (width >= 40rem) {
      max-height: calc(90vh - 140px);
    }
  }
  .sm\:max-w-\[60\%\] {
    @media (width >= 40rem) {
      max-width: 60%;
    }
  }
  .sm\:max-w-\[65\%\] {
    @media (width >= 40rem) {
      max-width: 65%;
    }
  }
  .sm\:gap-3 {
    @media (width >= 40rem) {
      gap: calc(var(--spacing) * 3);
    }
  }
  .sm\:rounded-xl {
    @media (width >= 40rem) {
      border-radius: var(--radius-xl);
    }
  }
  .sm\:p-4 {
    @media (width >= 40rem) {
      padding: calc(var(--spacing) * 4);
    }
  }
  .sm\:text-lg {
    @media (width >= 40rem) {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }
  .lg\:relative {
    @media (width >= 64rem) {
      position: relative;
    }
  }
  .lg\:static {
    @media (width >= 64rem) {
      position: static;
    }
  }
  .lg\:inset-auto {
    @media (width >= 64rem) {
      inset: auto;
    }
  }
  .lg\:top-0 {
    @media (width >= 64rem) {
      top: calc(var(--spacing) * 0);
    }
  }
  .lg\:right-0 {
    @media (width >= 64rem) {
      right: calc(var(--spacing) * 0);
    }
  }
  .lg\:z-auto {
    @media (width >= 64rem) {
      z-index: auto;
    }
  }
  .lg\:mb-4 {
    @media (width >= 64rem) {
      margin-bottom: calc(var(--spacing) * 4);
    }
  }
  .lg\:block {
    @media (width >= 64rem) {
      display: block;
    }
  }
  .lg\:inline {
    @media (width >= 64rem) {
      display: inline;
    }
  }
  .lg\:h-\[85vh\] {
    @media (width >= 64rem) {
      height: 85vh;
    }
  }
  .lg\:h-auto {
    @media (width >= 64rem) {
      height: auto;
    }
  }
  .lg\:min-h-screen {
    @media (width >= 64rem) {
      min-height: 100vh;
    }
  }
  .lg\:w-80 {
    @media (width >= 64rem) {
      width: calc(var(--spacing) * 80);
    }
  }
  .lg\:max-w-\[50\%\] {
    @media (width >= 64rem) {
      max-width: 50%;
    }
  }
  .lg\:max-w-\[55\%\] {
    @media (width >= 64rem) {
      max-width: 55%;
    }
  }
  .lg\:flex-shrink-0 {
    @media (width >= 64rem) {
      flex-shrink: 0;
    }
  }
  .lg\:translate-x-0 {
    @media (width >= 64rem) {
      --tw-translate-x: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .lg\:transform-none {
    @media (width >= 64rem) {
      transform: none;
    }
  }
  .lg\:flex-row {
    @media (width >= 64rem) {
      flex-direction: row;
    }
  }
  .lg\:gap-2 {
    @media (width >= 64rem) {
      gap: calc(var(--spacing) * 2);
    }
  }
  .lg\:gap-4 {
    @media (width >= 64rem) {
      gap: calc(var(--spacing) * 4);
    }
  }
  .lg\:overflow-auto {
    @media (width >= 64rem) {
      overflow: auto;
    }
  }
  .lg\:rounded-xl {
    @media (width >= 64rem) {
      border-radius: var(--radius-xl);
    }
  }
  .lg\:border {
    @media (width >= 64rem) {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }
  .lg\:p-2 {
    @media (width >= 64rem) {
      padding: calc(var(--spacing) * 2);
    }
  }
  .lg\:p-3 {
    @media (width >= 64rem) {
      padding: calc(var(--spacing) * 3);
    }
  }
  .lg\:p-4 {
    @media (width >= 64rem) {
      padding: calc(var(--spacing) * 4);
    }
  }
  .lg\:px-2 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 2);
    }
  }
  .lg\:px-4 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 4);
    }
  }
  .lg\:py-3 {
    @media (width >= 64rem) {
      padding-block: calc(var(--spacing) * 3);
    }
  }
  .lg\:text-lg {
    @media (width >= 64rem) {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }
  .lg\:text-sm {
    @media (width >= 64rem) {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }
  .lg\:shadow-2xl {
    @media (width >= 64rem) {
      --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .dark\:border-blue-500 {
    @media (prefers-color-scheme: dark) {
      border-color: var(--color-blue-500);
    }
  }
  .dark\:border-gray-500 {
    @media (prefers-color-scheme: dark) {
      border-color: var(--color-gray-500);
    }
  }
  .dark\:border-gray-600 {
    @media (prefers-color-scheme: dark) {
      border-color: var(--color-gray-600);
    }
  }
  .dark\:border-orange-600 {
    @media (prefers-color-scheme: dark) {
      border-color: var(--color-orange-600);
    }
  }
  .dark\:border-purple-500 {
    @media (prefers-color-scheme: dark) {
      border-color: var(--color-purple-500);
    }
  }
  .dark\:border-red-400 {
    @media (prefers-color-scheme: dark) {
      border-color: var(--color-red-400);
    }
  }
  .dark\:border-red-600 {
    @media (prefers-color-scheme: dark) {
      border-color: var(--color-red-600);
    }
  }
  .dark\:border-slate-400\/10 {
    @media (prefers-color-scheme: dark) {
      border-color: color-mix(in srgb, oklch(70.4% 0.04 256.788) 10%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        border-color: color-mix(in oklab, var(--color-slate-400) 10%, transparent);
      }
    }
  }
  .dark\:border-slate-400\/20 {
    @media (prefers-color-scheme: dark) {
      border-color: color-mix(in srgb, oklch(70.4% 0.04 256.788) 20%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        border-color: color-mix(in oklab, var(--color-slate-400) 20%, transparent);
      }
    }
  }
  .dark\:border-slate-500 {
    @media (prefers-color-scheme: dark) {
      border-color: var(--color-slate-500);
    }
  }
  .dark\:border-slate-600 {
    @media (prefers-color-scheme: dark) {
      border-color: var(--color-slate-600);
    }
  }
  .dark\:border-yellow-500 {
    @media (prefers-color-scheme: dark) {
      border-color: var(--color-yellow-500);
    }
  }
  .dark\:border-yellow-600 {
    @media (prefers-color-scheme: dark) {
      border-color: var(--color-yellow-600);
    }
  }
  .dark\:bg-amber-600 {
    @media (prefers-color-scheme: dark) {
      background-color: var(--color-amber-600);
    }
  }
  .dark\:bg-emerald-600 {
    @media (prefers-color-scheme: dark) {
      background-color: var(--color-emerald-600);
    }
  }
  .dark\:bg-gray-800 {
    @media (prefers-color-scheme: dark) {
      background-color: var(--color-gray-800);
    }
  }
  .dark\:bg-orange-900\/30 {
    @media (prefers-color-scheme: dark) {
      background-color: color-mix(in srgb, oklch(40.8% 0.123 38.172) 30%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-orange-900) 30%, transparent);
      }
    }
  }
  .dark\:bg-red-600 {
    @media (prefers-color-scheme: dark) {
      background-color: var(--color-red-600);
    }
  }
  .dark\:bg-red-800 {
    @media (prefers-color-scheme: dark) {
      background-color: var(--color-red-800);
    }
  }
  .dark\:bg-red-900\/30 {
    @media (prefers-color-scheme: dark) {
      background-color: color-mix(in srgb, oklch(39.6% 0.141 25.723) 30%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-red-900) 30%, transparent);
      }
    }
  }
  .dark\:bg-sky-600 {
    @media (prefers-color-scheme: dark) {
      background-color: var(--color-sky-600);
    }
  }
  .dark\:bg-slate-500 {
    @media (prefers-color-scheme: dark) {
      background-color: var(--color-slate-500);
    }
  }
  .dark\:bg-slate-600 {
    @media (prefers-color-scheme: dark) {
      background-color: var(--color-slate-600);
    }
  }
  .dark\:bg-slate-700 {
    @media (prefers-color-scheme: dark) {
      background-color: var(--color-slate-700);
    }
  }
  .dark\:bg-yellow-900\/30 {
    @media (prefers-color-scheme: dark) {
      background-color: color-mix(in srgb, oklch(42.1% 0.095 57.708) 30%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-yellow-900) 30%, transparent);
      }
    }
  }
  .dark\:from-orange-400 {
    @media (prefers-color-scheme: dark) {
      --tw-gradient-from: var(--color-orange-400);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }
  .dark\:from-red-400 {
    @media (prefers-color-scheme: dark) {
      --tw-gradient-from: var(--color-red-400);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }
  .dark\:to-orange-500 {
    @media (prefers-color-scheme: dark) {
      --tw-gradient-to: var(--color-orange-500);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }
  .dark\:to-red-500 {
    @media (prefers-color-scheme: dark) {
      --tw-gradient-to: var(--color-red-500);
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }
  }
  .dark\:text-gray-300 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-gray-300);
    }
  }
  .dark\:text-orange-300 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-orange-300);
    }
  }
  .dark\:text-red-300 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-red-300);
    }
  }
  .dark\:text-sky-300 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-sky-300);
    }
  }
  .dark\:text-slate-200 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-slate-200);
    }
  }
  .dark\:text-slate-300 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-slate-300);
    }
  }
  .dark\:text-teal-300 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-teal-300);
    }
  }
  .dark\:text-yellow-300 {
    @media (prefers-color-scheme: dark) {
      color: var(--color-yellow-300);
    }
  }
  .dark\:shadow-2xl {
    @media (prefers-color-scheme: dark) {
      --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .dark\:shadow-lg {
    @media (prefers-color-scheme: dark) {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .dark\:hover\:bg-amber-500 {
    @media (prefers-color-scheme: dark) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-amber-500);
        }
      }
    }
  }
  .dark\:hover\:bg-emerald-500 {
    @media (prefers-color-scheme: dark) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-emerald-500);
        }
      }
    }
  }
  .dark\:hover\:bg-red-500 {
    @media (prefers-color-scheme: dark) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-red-500);
        }
      }
    }
  }
  .dark\:hover\:bg-red-700 {
    @media (prefers-color-scheme: dark) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-red-700);
        }
      }
    }
  }
  .dark\:hover\:bg-sky-500 {
    @media (prefers-color-scheme: dark) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-sky-500);
        }
      }
    }
  }
  .dark\:hover\:bg-slate-400 {
    @media (prefers-color-scheme: dark) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-slate-400);
        }
      }
    }
  }
}
:root {
  --bg-primary: 255 255 255;
  --bg-secondary: 249 250 251;
  --bg-tertiary: 243 244 246;
  --bg-quaternary: 229 231 235;
  --text-primary: 17 24 39;
  --text-secondary: 75 85 99;
  --text-tertiary: 156 163 175;
  --text-quaternary: 107 114 128;
  --border-primary: 229 231 235;
  --border-secondary: 209 213 219;
  --border-tertiary: 156 163 175;
  --accent-primary: 59 130 246;
  --accent-secondary: 34 197 94;
  --accent-tertiary: 168 85 247;
  --accent-warning: 245 158 11;
  --accent-error: 239 68 68;
  --surface-primary: 255 255 255;
  --surface-secondary: 249 250 251;
  --surface-tertiary: 243 244 246;
  --interactive-primary: 59 130 246;
  --interactive-secondary: 243 244 246;
  --interactive-hover: 229 231 235;
  --terminal-bg: 248 250 252;
  --terminal-text: 30 41 59;
  --terminal-border: 203 213 225;
  --terminal-success-bg: 240 253 244;
  --terminal-success-text: 22 101 52;
  --terminal-success-border: 34 197 94;
  --terminal-error-bg: 254 242 242;
  --terminal-error-text: 153 27 27;
  --terminal-error-border: 239 68 68;
  --terminal-header-bg: 241 245 249;
  --terminal-header-text: 51 65 85;
}
:root.dark {
  --bg-primary: 15 23 42;
  --bg-secondary: 30 41 59;
  --bg-tertiary: 51 65 85;
  --bg-quaternary: 71 85 105;
  --text-primary: 248 250 252;
  --text-secondary: 203 213 225;
  --text-tertiary: 148 163 184;
  --text-quaternary: 100 116 139;
  --border-primary: 51 65 85;
  --border-secondary: 71 85 105;
  --border-tertiary: 100 116 139;
  --accent-primary: 56 189 248;
  --accent-secondary: 34 197 94;
  --accent-tertiary: 168 85 247;
  --accent-warning: 251 191 36;
  --accent-error: 248 113 113;
  --surface-primary: 30 41 59;
  --surface-secondary: 51 65 85;
  --surface-tertiary: 71 85 105;
  --interactive-primary: 56 189 248;
  --interactive-secondary: 71 85 105;
  --interactive-hover: 100 116 139;
  --terminal-bg: 15 23 42;
  --terminal-text: ***********;
  --terminal-border: 71 85 105;
  --terminal-success-bg: 20 83 45;
  --terminal-success-text: 134 239 172;
  --terminal-success-border: 34 197 94;
  --terminal-error-bg: 127 29 29;
  --terminal-error-text: 252 165 165;
  --terminal-error-border: 239 68 68;
  --terminal-header-bg: 30 41 59;
  --terminal-header-text: 203 213 225;
}
html {
  -webkit-text-size-adjust: 100%;
  scroll-behavior: smooth;
}
body {
  -webkit-overflow-scrolling: touch;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: rgb(var(--bg-primary));
  color: rgb(var(--text-primary));
}
body.theme-transitions-enabled {
  transition: background-color var(--duration-normal) var(--ease-out),
              color var(--duration-normal) var(--ease-out);
}
html.dark body {
  background-color: rgb(15 23 42);
  color: rgb(248 250 252);
}
html:not(.no-transitions) * {
  transition-property: background-color, border-color, color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
html.no-transitions * {
  transition: none !important;
}
button, [role="button"] {
  touch-action: manipulation;
}
*:focus-visible {
  outline: 2px solid rgb(var(--color-primary));
  outline-offset: 2px;
  border-radius: 4px;
}
button, input, select, textarea {
  transition: all var(--duration-fast) var(--ease-out);
}
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
::-webkit-scrollbar-track {
  background: rgb(243 244 246);
}
::-webkit-scrollbar-thumb {
  background: rgb(156 163 175);
  border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
  background: rgb(107 114 128);
}
:root.dark ::-webkit-scrollbar-track {
  background: rgb(31 41 55);
}
:root.dark ::-webkit-scrollbar-thumb {
  background: rgb(75 85 99);
}
:root.dark ::-webkit-scrollbar-thumb:hover {
  background: rgb(107 114 128);
}
.touch-manipulation {
  touch-action: manipulation;
}
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}
.select-none-touch {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
}
.touch-target-large {
  min-height: 44px;
  min-width: 44px;
}
.touch-target-medium {
  min-height: 36px;
  min-width: 36px;
}
@media (max-width: 640px) {
  .mobile-full-width {
    width: 100% !important;
  }
  .mobile-full-height {
    height: 100vh !important;
    max-height: 100vh !important;
  }
  .mobile-no-rounded {
    border-radius: 0 !important;
  }
  .mobile-p-4 {
    padding: 1rem !important;
  }
  .mobile-text-base {
    font-size: 1rem !important;
  }
}
.animate-fade-in {
  animation: fadeIn var(--duration-normal) var(--ease-out);
}
.animate-slide-up {
  animation: slideUp var(--duration-normal) var(--ease-out);
}
.animate-slide-down {
  animation: slideDown var(--duration-normal) var(--ease-out);
}
.animate-scale-in {
  animation: scaleIn var(--duration-normal) var(--ease-bounce);
}
.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
.animation-delay-200 {
  animation-delay: 200ms;
}
.animation-delay-400 {
  animation-delay: 400ms;
}
.animation-delay-600 {
  animation-delay: 600ms;
}
@keyframes modalFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes modalScaleIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
.animate-modal-fade-in {
  animation: modalFadeIn var(--duration-normal) var(--ease-out);
}
.animate-modal-scale-in {
  animation: modalScaleIn var(--duration-normal) var(--ease-out);
}
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
.gradient-primary {
  background: linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-primary-dark)));
}
.gradient-success {
  background: linear-gradient(135deg, rgb(var(--color-success)), rgb(34 197 94));
}
.gradient-error {
  background: linear-gradient(135deg, rgb(var(--color-error)), rgb(220 38 38));
}
.shadow-glow {
  box-shadow: 0 0 20px rgba(var(--color-primary), 0.3);
}
.shadow-glow-success {
  box-shadow: 0 0 20px rgba(var(--color-success), 0.3);
}
.shadow-glow-error {
  box-shadow: 0 0 20px rgba(var(--color-error), 0.3);
}
.bg-theme-primary {
  background-color: rgb(var(--bg-primary));
}
.bg-theme-secondary {
  background-color: rgb(var(--bg-secondary));
}
.bg-theme-tertiary {
  background-color: rgb(var(--bg-tertiary));
}
.bg-theme-quaternary {
  background-color: rgb(var(--bg-quaternary));
}
.bg-surface-primary {
  background-color: rgb(var(--surface-primary));
}
.bg-surface-secondary {
  background-color: rgb(var(--surface-secondary));
}
.bg-surface-tertiary {
  background-color: rgb(var(--surface-tertiary));
}
.text-theme-primary {
  color: rgb(var(--text-primary));
}
.text-theme-secondary {
  color: rgb(var(--text-secondary));
}
.text-theme-tertiary {
  color: rgb(var(--text-tertiary));
}
.text-theme-quaternary {
  color: rgb(var(--text-quaternary));
}
.border-theme-primary {
  border-color: rgb(var(--border-primary));
}
.border-theme-secondary {
  border-color: rgb(var(--border-secondary));
}
.border-theme-tertiary {
  border-color: rgb(var(--border-tertiary));
}
.text-accent-primary {
  color: rgb(var(--accent-primary));
}
.text-accent-secondary {
  color: rgb(var(--accent-secondary));
}
.text-accent-tertiary {
  color: rgb(var(--accent-tertiary));
}
.text-accent-warning {
  color: rgb(var(--accent-warning));
}
.text-accent-error {
  color: rgb(var(--accent-error));
}
.bg-accent-primary {
  background-color: rgb(var(--accent-primary));
}
.bg-accent-secondary {
  background-color: rgb(var(--accent-secondary));
}
.bg-terminal-bg {
  background-color: rgb(var(--terminal-bg)) !important;
  color: rgb(var(--terminal-text)) !important;
}
.bg-interactive-primary {
  background-color: rgb(var(--interactive-primary));
}
.bg-interactive-secondary {
  background-color: rgb(var(--interactive-secondary));
}
.hover\:bg-interactive-hover:hover {
  background-color: rgb(var(--interactive-hover));
}
.hover\:bg-theme-tertiary:hover {
  background-color: rgb(var(--bg-tertiary));
}
.btn-primary {
  background: linear-gradient(135deg, rgb(var(--color-primary)), rgb(var(--color-primary-dark)));
  color: white;
  border: none;
  transition: all var(--duration-fast) var(--ease-out);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--color-primary), 0.4);
}
.btn-secondary {
  background-color: rgb(var(--bg-secondary));
  color: rgb(var(--text-primary));
  border: 1px solid rgb(var(--border-primary));
  transition: all var(--duration-fast) var(--ease-out);
}
.btn-secondary:hover {
  background-color: rgb(var(--bg-tertiary));
  border-color: rgb(var(--border-secondary));
}
.btn-interactive {
  background-color: rgb(var(--interactive-secondary));
  color: rgb(var(--text-primary));
  border: 1px solid rgb(var(--border-primary));
  transition: all var(--duration-fast) var(--ease-out);
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
}
.btn-interactive:hover {
  background-color: rgb(var(--interactive-hover));
  border-color: rgb(var(--border-secondary));
  transform: translateY(-1px);
}
.btn-accent {
  background-color: rgb(var(--accent-primary));
  color: white;
  border: none;
  transition: all var(--duration-fast) var(--ease-out);
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
}
.btn-accent:hover {
  background-color: rgb(var(--accent-primary));
  opacity: 0.9;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--accent-primary), 0.3);
}
.card {
  background-color: rgb(var(--bg-primary));
  border: 1px solid rgb(var(--border-primary));
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all var(--duration-normal) var(--ease-out);
}
.card:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}
:root.dark .card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
  border-color: rgb(var(--border-primary));
}
:root.dark .card:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.5);
  border-color: rgb(var(--border-secondary));
}
.message-bubble-ai {
  background-color: rgb(var(--surface-secondary));
  color: rgb(var(--text-primary));
  border: 1px solid rgb(var(--border-primary));
}
.message-bubble-system {
  background-color: rgb(var(--surface-tertiary));
  color: rgb(var(--text-primary));
  border: 1px solid rgb(var(--border-secondary));
}
.message-bubble-user {
  background: linear-gradient(135deg, rgb(var(--accent-primary)), rgb(var(--accent-primary)));
  color: white;
}
.message-bubble-ai *,
  .message-bubble-system * {
  color: inherit !important;
}
.message-bubble-user * {
  color: white !important;
}
.message-bubble-ai .markdown-content,
  .message-bubble-system .markdown-content {
  color: rgb(var(--text-primary));
}
.message-bubble-ai .markdown-content *,
  .message-bubble-system .markdown-content * {
  color: rgb(var(--text-primary)) !important;
}
.message-bubble-user .markdown-content,
  .message-bubble-user .markdown-content * {
  color: white !important;
}
.card-surface {
  background-color: rgb(var(--surface-primary));
  border: 1px solid rgb(var(--border-primary));
  border-radius: 0.75rem;
  transition: all var(--duration-normal) var(--ease-out);
}
.card-surface-secondary {
  background-color: rgb(var(--surface-secondary));
  border: 1px solid rgb(var(--border-primary));
  border-radius: 0.5rem;
  transition: all var(--duration-normal) var(--ease-out);
}
.card-surface-tertiary {
  background-color: rgb(var(--surface-tertiary));
  border: 1px solid rgb(var(--border-secondary));
  border-radius: 0.5rem;
  transition: all var(--duration-normal) var(--ease-out);
}
.markdown-content {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}
.markdown-content code {
  word-break: break-word;
  white-space: pre-wrap;
  background-color: rgb(var(--bg-tertiary));
  color: rgb(var(--text-primary));
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
  border: 1px solid rgb(var(--border-primary));
}
.markdown-content pre {
  background-color: rgb(var(--surface-tertiary));
  color: rgb(var(--text-primary));
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 0.5rem 0;
  border: 1px solid rgb(var(--border-primary));
}
.markdown-content li {
  word-wrap: break-word;
  overflow-wrap: break-word;
}
.markdown-content blockquote {
  border-left: 4px solid rgb(var(--color-primary));
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: rgb(var(--text-secondary));
}
.markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 1rem 0;
}
.markdown-content th,
  .markdown-content td {
  border: 1px solid rgb(var(--border-primary));
  padding: 0.5rem;
  text-align: left;
}
.markdown-content th {
  background-color: rgb(var(--bg-secondary));
  font-weight: 600;
}
.clip-path-message-tail {
  clip-path: polygon(0 0, 0% 100%, 100% 0);
}
.clip-path-message-tail-user {
  clip-path: polygon(100% 0, 0 0, 100% 100%);
}
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}
@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(var(--color-primary), 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(var(--color-primary), 0.8);
  }
}
.animate-fadeIn {
  animation: fadeIn var(--duration-normal) var(--ease-out);
}
@media (max-width: 640px) {
  .overflow-y-auto {
    -webkit-overflow-scrolling: touch;
  }
  .touch-target-large {
    min-height: 44px;
    min-width: 44px;
  }
  button {
    box-sizing: border-box;
  }
}
.terminal-output {
  background-color: rgb(var(--terminal-bg));
  color: rgb(var(--terminal-text));
  border: 1px solid rgb(var(--terminal-border));
  border-radius: 0.5rem;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.2;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all var(--duration-fast) var(--ease-out);
  font-feature-settings: "liga" 0, "calt" 0;
  font-variant-ligatures: none;
}
:root.dark .terminal-output {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}
.terminal-header {
  background-color: rgb(var(--terminal-header-bg));
  color: rgb(var(--terminal-header-text));
  padding: 0.5rem 0.75rem;
  border-bottom: 1px solid rgb(var(--terminal-border));
  font-weight: 600;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.terminal-content {
  padding: 0.75rem;
  background-color: rgb(var(--terminal-bg));
  color: rgb(var(--terminal-text));
}
.terminal-success {
  background-color: rgb(var(--terminal-success-bg));
  color: rgb(var(--terminal-success-text));
  border-color: rgb(var(--terminal-success-border));
}
.terminal-success .terminal-header {
  background-color: rgb(var(--terminal-success-bg));
  color: rgb(var(--terminal-success-text));
  border-bottom-color: rgb(var(--terminal-success-border));
}
:root.dark .terminal-success .terminal-header {
  background-color: rgb(22 101 52);
  color: rgb(187 247 208);
  border-bottom-color: rgb(34 197 94);
}
.terminal-error {
  background-color: rgb(var(--terminal-error-bg));
  color: rgb(var(--terminal-error-text));
  border-color: rgb(var(--terminal-error-border));
}
.terminal-error .terminal-header {
  background-color: rgb(var(--terminal-error-bg));
  color: rgb(var(--terminal-error-text));
  border-bottom-color: rgb(var(--terminal-error-border));
}
:root.dark .terminal-error .terminal-header {
  background-color: rgb(153 27 27);
  color: rgb(254 202 202);
  border-bottom-color: rgb(239 68 68);
}
.terminal-pre {
  background: transparent;
  color: inherit;
  margin: 0;
  padding: 0;
  border: none;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.2;
  white-space: pre;
  word-wrap: normal;
  overflow-wrap: normal;
  max-height: 20rem;
  overflow-y: auto;
  overflow-x: auto;
  tab-size: 8;
  -moz-tab-size: 8;
}
.terminal-details {
  margin: 0.5rem 0;
}
.terminal-details summary {
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  background-color: rgba(var(--terminal-border), 0.1);
  border-radius: 0.25rem;
  font-weight: 500;
  transition: all var(--duration-fast) var(--ease-out);
  display: flex;
  align-items: center;
  gap: 0.25rem;
}
.terminal-details summary:hover {
  background-color: rgba(var(--terminal-border), 0.2);
}
.terminal-details[open] summary {
  margin-bottom: 0.5rem;
  border-bottom: 1px solid rgba(var(--terminal-border), 0.3);
}
.terminal-stdout {
  background-color: rgb(var(--terminal-bg));
  color: rgb(var(--terminal-text));
  border: 1px solid rgb(var(--terminal-border));
}
.terminal-stderr {
  background-color: rgb(var(--terminal-error-bg));
  color: rgb(var(--terminal-error-text));
  border: 1px solid rgb(var(--terminal-error-border));
}
.terminal-pre::-webkit-scrollbar {
  width: 6px;
}
.terminal-pre::-webkit-scrollbar-track {
  background: rgba(var(--terminal-border), 0.1);
  border-radius: 3px;
}
.terminal-pre::-webkit-scrollbar-thumb {
  background: rgba(var(--terminal-border), 0.3);
  border-radius: 3px;
}
.terminal-pre::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--terminal-border), 0.5);
}
.terminal-command {
  border: 1px solid rgb(71 85 105);
  border-radius: 0.375rem;
  padding: 0.75rem;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
  font-size: 0.875rem;
  line-height: 1.4;
  overflow-x: auto;
  position: relative;
  word-wrap: break-word;
  white-space: pre-wrap;
  background-color: rgb(15 23 42) !important;
  color: rgb(***********) !important;
}
.terminal-command::before {
  content: '$ ';
  color: rgb(56 189 248) !important;
  font-weight: 600;
}
.terminal-command * {
  color: rgb(***********) !important;
}
:root.dark .terminal-command {
  background-color: rgb(15 23 42) !important;
  color: rgb(***********) !important;
  border-color: rgb(71 85 105);
}
:root.dark .terminal-command::before {
  color: rgb(56 189 248) !important;
}
:root.dark .terminal-command * {
  color: rgb(***********) !important;
}
div.terminal-command,
.terminal-command,
[class*="terminal-command"] {
  background-color: rgb(15 23 42) !important;
  color: rgb(***********) !important;
}
div.terminal-command *,
.terminal-command *,
[class*="terminal-command"] * {
  color: rgb(***********) !important;
}
.terminal-status-success {
  color: rgb(var(--terminal-success-text));
}
.terminal-status-error {
  color: rgb(var(--terminal-error-text));
}
.terminal-exit-code {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.75rem;
  opacity: 0.8;
}
.terminal-output.group:hover .terminal-modal-button {
  opacity: 1;
}
.terminal-modal-button {
  opacity: 0;
  transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
}
.terminal-modal-button:hover {
  transform: scale(1.05);
}
.terminal-details summary {
  position: relative;
  padding-right: 2.5rem;
  cursor: pointer;
  list-style: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.terminal-details summary::-webkit-details-marker {
  display: none;
}
.terminal-details summary::marker {
  display: none;
}
.terminal-details {
  margin-bottom: 0.5rem;
}
.terminal-details:last-child {
  margin-bottom: 0;
}
.modal-backdrop {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}
.modal-enter {
  opacity: 0;
  transform: scale(0.95);
}
.modal-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 0.2s ease-out, transform 0.2s ease-out;
}
.modal-exit {
  opacity: 1;
  transform: scale(1);
}
.modal-exit-active {
  opacity: 0;
  transform: scale(0.95);
  transition: opacity 0.15s ease-in, transform 0.15s ease-in;
}
.terminal-modal-output {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
  font-size: 0.875rem;
  line-height: 1.3;
  white-space: pre;
  overflow-x: auto;
  overflow-y: auto;
  tab-size: 8;
  -moz-tab-size: 8;
  font-feature-settings: "liga" 0, "calt" 0;
  font-variant-ligatures: none;
  letter-spacing: 0;
  word-spacing: 0;
  color: rgb(var(--terminal-text)) !important;
  background-color: transparent;
  scrollbar-width: thin;
  scrollbar-color: rgb(var(--border-secondary)) transparent;
}
.terminal-modal-output::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.terminal-modal-output::-webkit-scrollbar-track {
  background: transparent;
}
.terminal-modal-output::-webkit-scrollbar-thumb {
  background: rgb(var(--border-secondary));
  border-radius: 3px;
}
.terminal-modal-output::-webkit-scrollbar-thumb:hover {
  background: rgb(var(--border-tertiary));
}
.terminal-modal-output.ps-output {
  font-size: 0.8rem;
  line-height: 1.1;
}
.bg-terminal-success-bg {
  background-color: rgb(var(--terminal-success-bg)) !important;
}
.bg-terminal-error-bg {
  background-color: rgb(var(--terminal-error-bg)) !important;
}
.text-terminal-success-text {
  color: rgb(var(--terminal-success-text)) !important;
}
.text-terminal-error-text {
  color: rgb(var(--terminal-error-text)) !important;
}
.border-terminal-success-border {
  border-color: rgb(var(--terminal-success-border)) !important;
}
.border-terminal-error-border {
  border-color: rgb(var(--terminal-error-border)) !important;
}
:root.dark .terminal-header {
  background-color: rgb(30 41 59) !important;
  color: rgb(203 213 225) !important;
  border-bottom-color: rgb(71 85 105) !important;
}
:root.dark .terminal-exit-code {
  color: rgb(148 163 184) !important;
  opacity: 1;
}
.terminal-header span,
.terminal-header .terminal-exit-code {
  color: inherit !important;
}
:root.dark .terminal-header span,
:root.dark .terminal-header .terminal-exit-code {
  color: rgb(203 213 225) !important;
}
:root.dark .terminal-modal-output {
  color: rgb(***********) !important;
}
:root.dark .bg-terminal-bg {
  background-color: rgb(15 23 42) !important;
  color: rgb(***********) !important;
}
:root.dark .terminal-pre,
:root.dark .terminal-stdout,
:root.dark .terminal-stderr {
  color: rgb(***********) !important;
  background-color: rgb(15 23 42) !important;
}
:root.dark .terminal-modal-output *,
:root.dark .bg-terminal-bg *,
:root.dark .terminal-pre *,
:root.dark .terminal-stdout *,
:root.dark .terminal-stderr * {
  color: rgb(***********) !important;
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-gradient-position {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}
@property --tw-gradient-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false;
}
@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}
@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}
@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-ordinal {
  syntax: "*";
  inherits: false;
}
@property --tw-slashed-zero {
  syntax: "*";
  inherits: false;
}
@property --tw-numeric-figure {
  syntax: "*";
  inherits: false;
}
@property --tw-numeric-spacing {
  syntax: "*";
  inherits: false;
}
@property --tw-numeric-fraction {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@keyframes pulse {
  50% {
    opacity: 0.5;
  }
}
@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-ordinal: initial;
      --tw-slashed-zero: initial;
      --tw-numeric-figure: initial;
      --tw-numeric-spacing: initial;
      --tw-numeric-fraction: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
    }
  }
}
