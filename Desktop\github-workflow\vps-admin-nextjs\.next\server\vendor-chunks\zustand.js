"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/zustand";
exports.ids = ["vendor-chunks/zustand"];
exports.modules = {

/***/ "(ssr)/./node_modules/zustand/esm/middleware.mjs":
/*!*************************************************!*\
  !*** ./node_modules/zustand/esm/middleware.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   combine: () => (/* binding */ combine),\n/* harmony export */   createJSONStorage: () => (/* binding */ createJSONStorage),\n/* harmony export */   devtools: () => (/* binding */ devtools),\n/* harmony export */   persist: () => (/* binding */ persist),\n/* harmony export */   redux: () => (/* binding */ redux),\n/* harmony export */   subscribeWithSelector: () => (/* binding */ subscribeWithSelector)\n/* harmony export */ });\nconst reduxImpl = (reducer, initial) => (set, _get, api) => {\n  api.dispatch = (action) => {\n    set((state) => reducer(state, action), false, action);\n    return action;\n  };\n  api.dispatchFromDevtools = true;\n  return { dispatch: (...args) => api.dispatch(...args), ...initial };\n};\nconst redux = reduxImpl;\n\nconst trackedConnections = /* @__PURE__ */ new Map();\nconst getTrackedConnectionState = (name) => {\n  const api = trackedConnections.get(name);\n  if (!api) return {};\n  return Object.fromEntries(\n    Object.entries(api.stores).map(([key, api2]) => [key, api2.getState()])\n  );\n};\nconst extractConnectionInformation = (store, extensionConnector, options) => {\n  if (store === void 0) {\n    return {\n      type: \"untracked\",\n      connection: extensionConnector.connect(options)\n    };\n  }\n  const existingConnection = trackedConnections.get(options.name);\n  if (existingConnection) {\n    return { type: \"tracked\", store, ...existingConnection };\n  }\n  const newConnection = {\n    connection: extensionConnector.connect(options),\n    stores: {}\n  };\n  trackedConnections.set(options.name, newConnection);\n  return { type: \"tracked\", store, ...newConnection };\n};\nconst removeStoreFromTrackedConnections = (name, store) => {\n  if (store === void 0) return;\n  const connectionInfo = trackedConnections.get(name);\n  if (!connectionInfo) return;\n  delete connectionInfo.stores[store];\n  if (Object.keys(connectionInfo.stores).length === 0) {\n    trackedConnections.delete(name);\n  }\n};\nconst findCallerName = (stack) => {\n  var _a, _b;\n  if (!stack) return void 0;\n  const traceLines = stack.split(\"\\n\");\n  const apiSetStateLineIndex = traceLines.findIndex(\n    (traceLine) => traceLine.includes(\"api.setState\")\n  );\n  if (apiSetStateLineIndex < 0) return void 0;\n  const callerLine = ((_a = traceLines[apiSetStateLineIndex + 1]) == null ? void 0 : _a.trim()) || \"\";\n  return (_b = /.+ (.+) .+/.exec(callerLine)) == null ? void 0 : _b[1];\n};\nconst devtoolsImpl = (fn, devtoolsOptions = {}) => (set, get, api) => {\n  const { enabled, anonymousActionType, store, ...options } = devtoolsOptions;\n  let extensionConnector;\n  try {\n    extensionConnector = (enabled != null ? enabled : ( false ? 0 : void 0) !== \"production\") && window.__REDUX_DEVTOOLS_EXTENSION__;\n  } catch (e) {\n  }\n  if (!extensionConnector) {\n    return fn(set, get, api);\n  }\n  const { connection, ...connectionInformation } = extractConnectionInformation(store, extensionConnector, options);\n  let isRecording = true;\n  api.setState = (state, replace, nameOrAction) => {\n    const r = set(state, replace);\n    if (!isRecording) return r;\n    const inferredActionType = findCallerName(new Error().stack);\n    const action = nameOrAction === void 0 ? { type: anonymousActionType || inferredActionType || \"anonymous\" } : typeof nameOrAction === \"string\" ? { type: nameOrAction } : nameOrAction;\n    if (store === void 0) {\n      connection == null ? void 0 : connection.send(action, get());\n      return r;\n    }\n    connection == null ? void 0 : connection.send(\n      {\n        ...action,\n        type: `${store}/${action.type}`\n      },\n      {\n        ...getTrackedConnectionState(options.name),\n        [store]: api.getState()\n      }\n    );\n    return r;\n  };\n  api.devtools = {\n    cleanup: () => {\n      if (connection && typeof connection.unsubscribe === \"function\") {\n        connection.unsubscribe();\n      }\n      removeStoreFromTrackedConnections(options.name, store);\n    }\n  };\n  const setStateFromDevtools = (...a) => {\n    const originalIsRecording = isRecording;\n    isRecording = false;\n    set(...a);\n    isRecording = originalIsRecording;\n  };\n  const initialState = fn(api.setState, get, api);\n  if (connectionInformation.type === \"untracked\") {\n    connection == null ? void 0 : connection.init(initialState);\n  } else {\n    connectionInformation.stores[connectionInformation.store] = api;\n    connection == null ? void 0 : connection.init(\n      Object.fromEntries(\n        Object.entries(connectionInformation.stores).map(([key, store2]) => [\n          key,\n          key === connectionInformation.store ? initialState : store2.getState()\n        ])\n      )\n    );\n  }\n  if (api.dispatchFromDevtools && typeof api.dispatch === \"function\") {\n    let didWarnAboutReservedActionType = false;\n    const originalDispatch = api.dispatch;\n    api.dispatch = (...args) => {\n      if (( false ? 0 : void 0) !== \"production\" && args[0].type === \"__setState\" && !didWarnAboutReservedActionType) {\n        console.warn(\n          '[zustand devtools middleware] \"__setState\" action type is reserved to set state from the devtools. Avoid using it.'\n        );\n        didWarnAboutReservedActionType = true;\n      }\n      originalDispatch(...args);\n    };\n  }\n  connection.subscribe((message) => {\n    var _a;\n    switch (message.type) {\n      case \"ACTION\":\n        if (typeof message.payload !== \"string\") {\n          console.error(\n            \"[zustand devtools middleware] Unsupported action format\"\n          );\n          return;\n        }\n        return parseJsonThen(\n          message.payload,\n          (action) => {\n            if (action.type === \"__setState\") {\n              if (store === void 0) {\n                setStateFromDevtools(action.state);\n                return;\n              }\n              if (Object.keys(action.state).length !== 1) {\n                console.error(\n                  `\n                    [zustand devtools middleware] Unsupported __setState action format.\n                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),\n                    and value of this only key should be a state object. Example: { \"type\": \"__setState\", \"state\": { \"abc123Store\": { \"foo\": \"bar\" } } }\n                    `\n                );\n              }\n              const stateFromDevtools = action.state[store];\n              if (stateFromDevtools === void 0 || stateFromDevtools === null) {\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(stateFromDevtools)) {\n                setStateFromDevtools(stateFromDevtools);\n              }\n              return;\n            }\n            if (!api.dispatchFromDevtools) return;\n            if (typeof api.dispatch !== \"function\") return;\n            api.dispatch(action);\n          }\n        );\n      case \"DISPATCH\":\n        switch (message.payload.type) {\n          case \"RESET\":\n            setStateFromDevtools(initialState);\n            if (store === void 0) {\n              return connection == null ? void 0 : connection.init(api.getState());\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"COMMIT\":\n            if (store === void 0) {\n              connection == null ? void 0 : connection.init(api.getState());\n              return;\n            }\n            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n          case \"ROLLBACK\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                connection == null ? void 0 : connection.init(api.getState());\n                return;\n              }\n              setStateFromDevtools(state[store]);\n              connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));\n            });\n          case \"JUMP_TO_STATE\":\n          case \"JUMP_TO_ACTION\":\n            return parseJsonThen(message.state, (state) => {\n              if (store === void 0) {\n                setStateFromDevtools(state);\n                return;\n              }\n              if (JSON.stringify(api.getState()) !== JSON.stringify(state[store])) {\n                setStateFromDevtools(state[store]);\n              }\n            });\n          case \"IMPORT_STATE\": {\n            const { nextLiftedState } = message.payload;\n            const lastComputedState = (_a = nextLiftedState.computedStates.slice(-1)[0]) == null ? void 0 : _a.state;\n            if (!lastComputedState) return;\n            if (store === void 0) {\n              setStateFromDevtools(lastComputedState);\n            } else {\n              setStateFromDevtools(lastComputedState[store]);\n            }\n            connection == null ? void 0 : connection.send(\n              null,\n              // FIXME no-any\n              nextLiftedState\n            );\n            return;\n          }\n          case \"PAUSE_RECORDING\":\n            return isRecording = !isRecording;\n        }\n        return;\n    }\n  });\n  return initialState;\n};\nconst devtools = devtoolsImpl;\nconst parseJsonThen = (stringified, fn) => {\n  let parsed;\n  try {\n    parsed = JSON.parse(stringified);\n  } catch (e) {\n    console.error(\n      \"[zustand devtools middleware] Could not parse the received json\",\n      e\n    );\n  }\n  if (parsed !== void 0) fn(parsed);\n};\n\nconst subscribeWithSelectorImpl = (fn) => (set, get, api) => {\n  const origSubscribe = api.subscribe;\n  api.subscribe = (selector, optListener, options) => {\n    let listener = selector;\n    if (optListener) {\n      const equalityFn = (options == null ? void 0 : options.equalityFn) || Object.is;\n      let currentSlice = selector(api.getState());\n      listener = (state) => {\n        const nextSlice = selector(state);\n        if (!equalityFn(currentSlice, nextSlice)) {\n          const previousSlice = currentSlice;\n          optListener(currentSlice = nextSlice, previousSlice);\n        }\n      };\n      if (options == null ? void 0 : options.fireImmediately) {\n        optListener(currentSlice, currentSlice);\n      }\n    }\n    return origSubscribe(listener);\n  };\n  const initialState = fn(set, get, api);\n  return initialState;\n};\nconst subscribeWithSelector = subscribeWithSelectorImpl;\n\nfunction combine(initialState, create) {\n  return (...args) => Object.assign({}, initialState, create(...args));\n}\n\nfunction createJSONStorage(getStorage, options) {\n  let storage;\n  try {\n    storage = getStorage();\n  } catch (e) {\n    return;\n  }\n  const persistStorage = {\n    getItem: (name) => {\n      var _a;\n      const parse = (str2) => {\n        if (str2 === null) {\n          return null;\n        }\n        return JSON.parse(str2, options == null ? void 0 : options.reviver);\n      };\n      const str = (_a = storage.getItem(name)) != null ? _a : null;\n      if (str instanceof Promise) {\n        return str.then(parse);\n      }\n      return parse(str);\n    },\n    setItem: (name, newValue) => storage.setItem(name, JSON.stringify(newValue, options == null ? void 0 : options.replacer)),\n    removeItem: (name) => storage.removeItem(name)\n  };\n  return persistStorage;\n}\nconst toThenable = (fn) => (input) => {\n  try {\n    const result = fn(input);\n    if (result instanceof Promise) {\n      return result;\n    }\n    return {\n      then(onFulfilled) {\n        return toThenable(onFulfilled)(result);\n      },\n      catch(_onRejected) {\n        return this;\n      }\n    };\n  } catch (e) {\n    return {\n      then(_onFulfilled) {\n        return this;\n      },\n      catch(onRejected) {\n        return toThenable(onRejected)(e);\n      }\n    };\n  }\n};\nconst persistImpl = (config, baseOptions) => (set, get, api) => {\n  let options = {\n    storage: createJSONStorage(() => localStorage),\n    partialize: (state) => state,\n    version: 0,\n    merge: (persistedState, currentState) => ({\n      ...currentState,\n      ...persistedState\n    }),\n    ...baseOptions\n  };\n  let hasHydrated = false;\n  const hydrationListeners = /* @__PURE__ */ new Set();\n  const finishHydrationListeners = /* @__PURE__ */ new Set();\n  let storage = options.storage;\n  if (!storage) {\n    return config(\n      (...args) => {\n        console.warn(\n          `[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`\n        );\n        set(...args);\n      },\n      get,\n      api\n    );\n  }\n  const setItem = () => {\n    const state = options.partialize({ ...get() });\n    return storage.setItem(options.name, {\n      state,\n      version: options.version\n    });\n  };\n  const savedSetState = api.setState;\n  api.setState = (state, replace) => {\n    savedSetState(state, replace);\n    void setItem();\n  };\n  const configResult = config(\n    (...args) => {\n      set(...args);\n      void setItem();\n    },\n    get,\n    api\n  );\n  api.getInitialState = () => configResult;\n  let stateFromStorage;\n  const hydrate = () => {\n    var _a, _b;\n    if (!storage) return;\n    hasHydrated = false;\n    hydrationListeners.forEach((cb) => {\n      var _a2;\n      return cb((_a2 = get()) != null ? _a2 : configResult);\n    });\n    const postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? void 0 : _b.call(options, (_a = get()) != null ? _a : configResult)) || void 0;\n    return toThenable(storage.getItem.bind(storage))(options.name).then((deserializedStorageValue) => {\n      if (deserializedStorageValue) {\n        if (typeof deserializedStorageValue.version === \"number\" && deserializedStorageValue.version !== options.version) {\n          if (options.migrate) {\n            const migration = options.migrate(\n              deserializedStorageValue.state,\n              deserializedStorageValue.version\n            );\n            if (migration instanceof Promise) {\n              return migration.then((result) => [true, result]);\n            }\n            return [true, migration];\n          }\n          console.error(\n            `State loaded from storage couldn't be migrated since no migrate function was provided`\n          );\n        } else {\n          return [false, deserializedStorageValue.state];\n        }\n      }\n      return [false, void 0];\n    }).then((migrationResult) => {\n      var _a2;\n      const [migrated, migratedState] = migrationResult;\n      stateFromStorage = options.merge(\n        migratedState,\n        (_a2 = get()) != null ? _a2 : configResult\n      );\n      set(stateFromStorage, true);\n      if (migrated) {\n        return setItem();\n      }\n    }).then(() => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);\n      stateFromStorage = get();\n      hasHydrated = true;\n      finishHydrationListeners.forEach((cb) => cb(stateFromStorage));\n    }).catch((e) => {\n      postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);\n    });\n  };\n  api.persist = {\n    setOptions: (newOptions) => {\n      options = {\n        ...options,\n        ...newOptions\n      };\n      if (newOptions.storage) {\n        storage = newOptions.storage;\n      }\n    },\n    clearStorage: () => {\n      storage == null ? void 0 : storage.removeItem(options.name);\n    },\n    getOptions: () => options,\n    rehydrate: () => hydrate(),\n    hasHydrated: () => hasHydrated,\n    onHydrate: (cb) => {\n      hydrationListeners.add(cb);\n      return () => {\n        hydrationListeners.delete(cb);\n      };\n    },\n    onFinishHydration: (cb) => {\n      finishHydrationListeners.add(cb);\n      return () => {\n        finishHydrationListeners.delete(cb);\n      };\n    }\n  };\n  if (!options.skipHydration) {\n    hydrate();\n  }\n  return stateFromStorage || configResult;\n};\nconst persist = persistImpl;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zustand/esm/middleware.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/zustand/esm/react.mjs":
/*!********************************************!*\
  !*** ./node_modules/zustand/esm/react.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var zustand_vanilla__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/vanilla */ \"(ssr)/./node_modules/zustand/esm/vanilla.mjs\");\n\n\n\nconst identity = (arg) => arg;\nfunction useStore(api, selector = identity) {\n  const slice = react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(\n    api.subscribe,\n    () => selector(api.getState()),\n    () => selector(api.getInitialState())\n  );\n  react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue(slice);\n  return slice;\n}\nconst createImpl = (createState) => {\n  const api = (0,zustand_vanilla__WEBPACK_IMPORTED_MODULE_1__.createStore)(createState);\n  const useBoundStore = (selector) => useStore(api, selector);\n  Object.assign(useBoundStore, api);\n  return useBoundStore;\n};\nconst create = (createState) => createState ? createImpl(createState) : createImpl;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvenVzdGFuZC9lc20vcmVhY3QubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMEI7QUFDb0I7O0FBRTlDO0FBQ0E7QUFDQSxnQkFBZ0IsdURBQTBCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRSxnREFBbUI7QUFDckI7QUFDQTtBQUNBO0FBQ0EsY0FBYyw0REFBVztBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUU0QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxNb2V0ZXpcXERlc2t0b3BcXGdpdGh1Yi13b3JrZmxvd1xcdnBzLWFkbWluLW5leHRqc1xcbm9kZV9tb2R1bGVzXFx6dXN0YW5kXFxlc21cXHJlYWN0Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgY3JlYXRlU3RvcmUgfSBmcm9tICd6dXN0YW5kL3ZhbmlsbGEnO1xuXG5jb25zdCBpZGVudGl0eSA9IChhcmcpID0+IGFyZztcbmZ1bmN0aW9uIHVzZVN0b3JlKGFwaSwgc2VsZWN0b3IgPSBpZGVudGl0eSkge1xuICBjb25zdCBzbGljZSA9IFJlYWN0LnVzZVN5bmNFeHRlcm5hbFN0b3JlKFxuICAgIGFwaS5zdWJzY3JpYmUsXG4gICAgKCkgPT4gc2VsZWN0b3IoYXBpLmdldFN0YXRlKCkpLFxuICAgICgpID0+IHNlbGVjdG9yKGFwaS5nZXRJbml0aWFsU3RhdGUoKSlcbiAgKTtcbiAgUmVhY3QudXNlRGVidWdWYWx1ZShzbGljZSk7XG4gIHJldHVybiBzbGljZTtcbn1cbmNvbnN0IGNyZWF0ZUltcGwgPSAoY3JlYXRlU3RhdGUpID0+IHtcbiAgY29uc3QgYXBpID0gY3JlYXRlU3RvcmUoY3JlYXRlU3RhdGUpO1xuICBjb25zdCB1c2VCb3VuZFN0b3JlID0gKHNlbGVjdG9yKSA9PiB1c2VTdG9yZShhcGksIHNlbGVjdG9yKTtcbiAgT2JqZWN0LmFzc2lnbih1c2VCb3VuZFN0b3JlLCBhcGkpO1xuICByZXR1cm4gdXNlQm91bmRTdG9yZTtcbn07XG5jb25zdCBjcmVhdGUgPSAoY3JlYXRlU3RhdGUpID0+IGNyZWF0ZVN0YXRlID8gY3JlYXRlSW1wbChjcmVhdGVTdGF0ZSkgOiBjcmVhdGVJbXBsO1xuXG5leHBvcnQgeyBjcmVhdGUsIHVzZVN0b3JlIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zustand/esm/react.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/zustand/esm/vanilla.mjs":
/*!**********************************************!*\
  !*** ./node_modules/zustand/esm/vanilla.mjs ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createStore: () => (/* binding */ createStore)\n/* harmony export */ });\nconst createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const api = { setState, getState, getInitialState, subscribe };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvenVzdGFuZC9lc20vdmFuaWxsYS5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEhBQThIO0FBQzlIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQjtBQUNBO0FBQ0E7QUFDQTs7QUFFdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTW9ldGV6XFxEZXNrdG9wXFxnaXRodWItd29ya2Zsb3dcXHZwcy1hZG1pbi1uZXh0anNcXG5vZGVfbW9kdWxlc1xcenVzdGFuZFxcZXNtXFx2YW5pbGxhLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBjcmVhdGVTdG9yZUltcGwgPSAoY3JlYXRlU3RhdGUpID0+IHtcbiAgbGV0IHN0YXRlO1xuICBjb25zdCBsaXN0ZW5lcnMgPSAvKiBAX19QVVJFX18gKi8gbmV3IFNldCgpO1xuICBjb25zdCBzZXRTdGF0ZSA9IChwYXJ0aWFsLCByZXBsYWNlKSA9PiB7XG4gICAgY29uc3QgbmV4dFN0YXRlID0gdHlwZW9mIHBhcnRpYWwgPT09IFwiZnVuY3Rpb25cIiA/IHBhcnRpYWwoc3RhdGUpIDogcGFydGlhbDtcbiAgICBpZiAoIU9iamVjdC5pcyhuZXh0U3RhdGUsIHN0YXRlKSkge1xuICAgICAgY29uc3QgcHJldmlvdXNTdGF0ZSA9IHN0YXRlO1xuICAgICAgc3RhdGUgPSAocmVwbGFjZSAhPSBudWxsID8gcmVwbGFjZSA6IHR5cGVvZiBuZXh0U3RhdGUgIT09IFwib2JqZWN0XCIgfHwgbmV4dFN0YXRlID09PSBudWxsKSA/IG5leHRTdGF0ZSA6IE9iamVjdC5hc3NpZ24oe30sIHN0YXRlLCBuZXh0U3RhdGUpO1xuICAgICAgbGlzdGVuZXJzLmZvckVhY2goKGxpc3RlbmVyKSA9PiBsaXN0ZW5lcihzdGF0ZSwgcHJldmlvdXNTdGF0ZSkpO1xuICAgIH1cbiAgfTtcbiAgY29uc3QgZ2V0U3RhdGUgPSAoKSA9PiBzdGF0ZTtcbiAgY29uc3QgZ2V0SW5pdGlhbFN0YXRlID0gKCkgPT4gaW5pdGlhbFN0YXRlO1xuICBjb25zdCBzdWJzY3JpYmUgPSAobGlzdGVuZXIpID0+IHtcbiAgICBsaXN0ZW5lcnMuYWRkKGxpc3RlbmVyKTtcbiAgICByZXR1cm4gKCkgPT4gbGlzdGVuZXJzLmRlbGV0ZShsaXN0ZW5lcik7XG4gIH07XG4gIGNvbnN0IGFwaSA9IHsgc2V0U3RhdGUsIGdldFN0YXRlLCBnZXRJbml0aWFsU3RhdGUsIHN1YnNjcmliZSB9O1xuICBjb25zdCBpbml0aWFsU3RhdGUgPSBzdGF0ZSA9IGNyZWF0ZVN0YXRlKHNldFN0YXRlLCBnZXRTdGF0ZSwgYXBpKTtcbiAgcmV0dXJuIGFwaTtcbn07XG5jb25zdCBjcmVhdGVTdG9yZSA9IChjcmVhdGVTdGF0ZSkgPT4gY3JlYXRlU3RhdGUgPyBjcmVhdGVTdG9yZUltcGwoY3JlYXRlU3RhdGUpIDogY3JlYXRlU3RvcmVJbXBsO1xuXG5leHBvcnQgeyBjcmVhdGVTdG9yZSB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/zustand/esm/vanilla.mjs\n");

/***/ })

};
;