import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Refresh<PERSON>w, Trash2, <PERSON><PERSON><PERSON>, CheckCircle, XCircle } from 'lucide-react';

interface TaskDebugInfo {
  task_id: string;
  status: string;
  title: string;
  age_minutes: number;
  current_step: number;
  estimated_steps: number;
  commands_executed: number;
  has_command_to_confirm: boolean;
  use_orchestrator: boolean;
}

interface DebugResponse {
  total_tasks: number;
  stuck_tasks: string[];
  task_details: Record<string, TaskDebugInfo>;
}

const TaskDebugPanel: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<DebugResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  const fetchDebugInfo = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/tasks/debug');
      if (response.ok) {
        const data = await response.json();
        setDebugInfo(data);
      } else {
        setError(`Failed to fetch debug info: ${response.status}`);
      }
    } catch (err) {
      setError(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const recoverStuckTasks = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/tasks/recover-stuck', { method: 'POST' });
      if (response.ok) {
        const data = await response.json();
        alert(data.message);
        await fetchDebugInfo(); // Refresh data
      } else {
        setError(`Failed to recover tasks: ${response.status}`);
      }
    } catch (err) {
      setError(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const resetTask = async (taskId: string) => {
    if (!confirm(`Reset task ${taskId.substring(0, 8)}...?`)) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/task/${taskId}/reset`, { method: 'POST' });
      if (response.ok) {
        const data = await response.json();
        alert(data.message);
        await fetchDebugInfo(); // Refresh data
      } else {
        setError(`Failed to reset task: ${response.status}`);
      }
    } catch (err) {
      setError(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const deleteTask = async (taskId: string) => {
    if (!confirm(`Delete task ${taskId.substring(0, 8)}...? This cannot be undone.`)) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/task/${taskId}`, { method: 'DELETE' });
      if (response.ok) {
        const data = await response.json();
        alert(data.message);
        await fetchDebugInfo(); // Refresh data
      } else {
        setError(`Failed to delete task: ${response.status}`);
      }
    } catch (err) {
      setError(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isVisible) {
      fetchDebugInfo();
    }
  }, [isVisible]);

  const getStatusColor = (status: string, isStuck: boolean) => {
    if (isStuck) return 'text-red-600';
    
    switch (status) {
      case 'COMPLETED': return 'text-green-600';
      case 'FAILED': 
      case 'ABORTED': return 'text-red-600';
      case 'AWAITING_COMMAND':
      case 'AWAITING_USER_INPUT':
      case 'AWAITING_USER_CONFIRMATION': return 'text-blue-600';
      default: return 'text-yellow-600';
    }
  };

  const getStatusIcon = (status: string, isStuck: boolean) => {
    if (isStuck) return <XCircle className="w-4 h-4 text-red-600" />;
    
    switch (status) {
      case 'COMPLETED': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'FAILED': 
      case 'ABORTED': return <XCircle className="w-4 h-4 text-red-600" />;
      default: return <RefreshCw className="w-4 h-4 text-yellow-600" />;
    }
  };

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 bg-gray-800 text-white p-2 rounded-full shadow-lg hover:bg-gray-700 transition-colors z-50"
        title="Open Task Debug Panel"
      >
        <Settings className="w-5 h-5" />
      </button>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[80vh] overflow-hidden">
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Task Debug Panel
          </h2>
          <button
            onClick={() => setIsVisible(false)}
            className="text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        </div>

        <div className="p-4">
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          <div className="flex gap-2 mb-4">
            <button
              onClick={fetchDebugInfo}
              disabled={loading}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
            
            {debugInfo && debugInfo.stuck_tasks.length > 0 && (
              <button
                onClick={recoverStuckTasks}
                disabled={loading}
                className="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700 disabled:opacity-50 flex items-center gap-2"
              >
                <AlertTriangle className="w-4 h-4" />
                Recover Stuck Tasks ({debugInfo.stuck_tasks.length})
              </button>
            )}
          </div>

          {debugInfo && (
            <div>
              <div className="mb-4 p-3 bg-gray-100 rounded">
                <p><strong>Total Tasks:</strong> {debugInfo.total_tasks}</p>
                <p><strong>Stuck Tasks:</strong> {debugInfo.stuck_tasks.length}</p>
              </div>

              <div className="overflow-y-auto max-h-96">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">Status</th>
                      <th className="text-left p-2">Task ID</th>
                      <th className="text-left p-2">Title</th>
                      <th className="text-left p-2">Age</th>
                      <th className="text-left p-2">Progress</th>
                      <th className="text-left p-2">Commands</th>
                      <th className="text-left p-2">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {Object.entries(debugInfo.task_details).map(([taskId, task]) => {
                      const isStuck = debugInfo.stuck_tasks.includes(taskId);
                      return (
                        <tr key={taskId} className={`border-b ${isStuck ? 'bg-red-50' : ''}`}>
                          <td className="p-2">
                            <div className="flex items-center gap-2">
                              {getStatusIcon(task.status, isStuck)}
                              <span className={getStatusColor(task.status, isStuck)}>
                                {task.status}
                              </span>
                            </div>
                          </td>
                          <td className="p-2 font-mono text-xs">
                            {taskId.substring(0, 8)}...
                          </td>
                          <td className="p-2 max-w-xs truncate" title={task.title}>
                            {task.title}
                          </td>
                          <td className="p-2">
                            {task.age_minutes.toFixed(1)}m
                          </td>
                          <td className="p-2">
                            {task.current_step}/{task.estimated_steps}
                          </td>
                          <td className="p-2">
                            {task.commands_executed}
                          </td>
                          <td className="p-2">
                            <div className="flex gap-1">
                              <button
                                onClick={() => resetTask(taskId)}
                                className="text-blue-600 hover:text-blue-800 p-1"
                                title="Reset Task"
                              >
                                <RefreshCw className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => deleteTask(taskId)}
                                className="text-red-600 hover:text-red-800 p-1"
                                title="Delete Task"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {loading && !debugInfo && (
            <div className="text-center py-8">
              <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-2" />
              <p>Loading debug information...</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TaskDebugPanel;
