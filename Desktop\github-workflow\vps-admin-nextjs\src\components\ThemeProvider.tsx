/**
 * Theme Provider Component
 * Ensures proper theme initialization and prevents hydration mismatches
 */

'use client';

import { useEffect, useState } from 'react';
import { useTheme } from '@/hooks/useTheme';

interface ThemeProviderProps {
  children: React.ReactNode;
}

export default function ThemeProvider({ children }: ThemeProviderProps) {
  const [mounted, setMounted] = useState(false);
  const { theme } = useTheme();

  // Ensure component is mounted before rendering to prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render children until mounted to prevent hydration issues
  if (!mounted) {
    return (
      <div className="flex items-center justify-center h-screen lg:min-h-screen bg-theme-secondary p-0 lg:p-4 overflow-hidden lg:overflow-auto">
        <div className="animate-pulse">Loading...</div>
      </div>
    );
  }

  return <>{children}</>;
}
