/**
 * Task management hook for VPS Admin Chat
 */

import { useState, useCallback } from 'react';
import { Task, UseTaskManagerReturn } from '@/types';
import { createTaskId, createTaskTitle } from '@/utils';
import { DEFAULTS } from '@/constants';

export const useTaskManager = (): UseTaskManagerReturn => {
  const [currentTask, setCurrentTask] = useState<Task | null>(null);
  const [taskHistory, setTaskHistory] = useState<Task[]>([]);
  const [taskId, setTaskId] = useState<string | null>(null);
  const [isTaskActive, setIsTaskActive] = useState<boolean>(false);

  const createTask = useCallback((title: string, description: string): Task => {
    const newTask: Task = {
      id: createTaskId(),
      title: title || createTaskTitle(description),
      description,
      status: 'pending',
      progress: DEFAULTS.PROGRESS,
      steps: [],
      startTime: new Date(),
      totalCommands: 0,
      successfulCommands: 0,
      failedCommands: 0
    };

    return newTask;
  }, []);

  const updateTaskProgress = useCallback((taskId: string, progress: number) => {
    setCurrentTask(prev => {
      if (prev && prev.id === taskId) {
        return { ...prev, progress: Math.min(100, Math.max(0, progress)) };
      }
      return prev;
    });
  }, []);



  return {
    // State
    currentTask,
    taskHistory,
    taskId,
    isTaskActive,

    // Basic setters
    setCurrentTask,
    setTaskHistory,
    setTaskId,
    setIsTaskActive,

    // Task operations
    createTask,
    updateTaskProgress
  };
};
