"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@microsoft";
exports.ids = ["vendor-chunks/@microsoft"];
exports.modules = {

/***/ "(ssr)/./node_modules/@microsoft/fetch-event-source/lib/esm/fetch.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@microsoft/fetch-event-source/lib/esm/fetch.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventStreamContentType: () => (/* binding */ EventStreamContentType),\n/* harmony export */   fetchEventSource: () => (/* binding */ fetchEventSource)\n/* harmony export */ });\n/* harmony import */ var _parse__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./parse */ \"(ssr)/./node_modules/@microsoft/fetch-event-source/lib/esm/parse.js\");\nvar __rest = (undefined && undefined.__rest) || function (s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n};\r\n\r\nconst EventStreamContentType = 'text/event-stream';\r\nconst DefaultRetryInterval = 1000;\r\nconst LastEventId = 'last-event-id';\r\nfunction fetchEventSource(input, _a) {\r\n    var { signal: inputSignal, headers: inputHeaders, onopen: inputOnOpen, onmessage, onclose, onerror, openWhenHidden, fetch: inputFetch } = _a, rest = __rest(_a, [\"signal\", \"headers\", \"onopen\", \"onmessage\", \"onclose\", \"onerror\", \"openWhenHidden\", \"fetch\"]);\r\n    return new Promise((resolve, reject) => {\r\n        const headers = Object.assign({}, inputHeaders);\r\n        if (!headers.accept) {\r\n            headers.accept = EventStreamContentType;\r\n        }\r\n        let curRequestController;\r\n        function onVisibilityChange() {\r\n            curRequestController.abort();\r\n            if (!document.hidden) {\r\n                create();\r\n            }\r\n        }\r\n        if (!openWhenHidden) {\r\n            document.addEventListener('visibilitychange', onVisibilityChange);\r\n        }\r\n        let retryInterval = DefaultRetryInterval;\r\n        let retryTimer = 0;\r\n        function dispose() {\r\n            document.removeEventListener('visibilitychange', onVisibilityChange);\r\n            window.clearTimeout(retryTimer);\r\n            curRequestController.abort();\r\n        }\r\n        inputSignal === null || inputSignal === void 0 ? void 0 : inputSignal.addEventListener('abort', () => {\r\n            dispose();\r\n            resolve();\r\n        });\r\n        const fetch = inputFetch !== null && inputFetch !== void 0 ? inputFetch : window.fetch;\r\n        const onopen = inputOnOpen !== null && inputOnOpen !== void 0 ? inputOnOpen : defaultOnOpen;\r\n        async function create() {\r\n            var _a;\r\n            curRequestController = new AbortController();\r\n            try {\r\n                const response = await fetch(input, Object.assign(Object.assign({}, rest), { headers, signal: curRequestController.signal }));\r\n                await onopen(response);\r\n                await (0,_parse__WEBPACK_IMPORTED_MODULE_0__.getBytes)(response.body, (0,_parse__WEBPACK_IMPORTED_MODULE_0__.getLines)((0,_parse__WEBPACK_IMPORTED_MODULE_0__.getMessages)(id => {\r\n                    if (id) {\r\n                        headers[LastEventId] = id;\r\n                    }\r\n                    else {\r\n                        delete headers[LastEventId];\r\n                    }\r\n                }, retry => {\r\n                    retryInterval = retry;\r\n                }, onmessage)));\r\n                onclose === null || onclose === void 0 ? void 0 : onclose();\r\n                dispose();\r\n                resolve();\r\n            }\r\n            catch (err) {\r\n                if (!curRequestController.signal.aborted) {\r\n                    try {\r\n                        const interval = (_a = onerror === null || onerror === void 0 ? void 0 : onerror(err)) !== null && _a !== void 0 ? _a : retryInterval;\r\n                        window.clearTimeout(retryTimer);\r\n                        retryTimer = window.setTimeout(create, interval);\r\n                    }\r\n                    catch (innerErr) {\r\n                        dispose();\r\n                        reject(innerErr);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        create();\r\n    });\r\n}\r\nfunction defaultOnOpen(response) {\r\n    const contentType = response.headers.get('content-type');\r\n    if (!(contentType === null || contentType === void 0 ? void 0 : contentType.startsWith(EventStreamContentType))) {\r\n        throw new Error(`Expected content-type to be ${EventStreamContentType}, Actual: ${contentType}`);\r\n    }\r\n}\r\n//# sourceMappingURL=fetch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/fetch-event-source/lib/esm/fetch.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@microsoft/fetch-event-source/lib/esm/parse.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@microsoft/fetch-event-source/lib/esm/parse.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBytes: () => (/* binding */ getBytes),\n/* harmony export */   getLines: () => (/* binding */ getLines),\n/* harmony export */   getMessages: () => (/* binding */ getMessages)\n/* harmony export */ });\nasync function getBytes(stream, onChunk) {\r\n    const reader = stream.getReader();\r\n    let result;\r\n    while (!(result = await reader.read()).done) {\r\n        onChunk(result.value);\r\n    }\r\n}\r\nfunction getLines(onLine) {\r\n    let buffer;\r\n    let position;\r\n    let fieldLength;\r\n    let discardTrailingNewline = false;\r\n    return function onChunk(arr) {\r\n        if (buffer === undefined) {\r\n            buffer = arr;\r\n            position = 0;\r\n            fieldLength = -1;\r\n        }\r\n        else {\r\n            buffer = concat(buffer, arr);\r\n        }\r\n        const bufLength = buffer.length;\r\n        let lineStart = 0;\r\n        while (position < bufLength) {\r\n            if (discardTrailingNewline) {\r\n                if (buffer[position] === 10) {\r\n                    lineStart = ++position;\r\n                }\r\n                discardTrailingNewline = false;\r\n            }\r\n            let lineEnd = -1;\r\n            for (; position < bufLength && lineEnd === -1; ++position) {\r\n                switch (buffer[position]) {\r\n                    case 58:\r\n                        if (fieldLength === -1) {\r\n                            fieldLength = position - lineStart;\r\n                        }\r\n                        break;\r\n                    case 13:\r\n                        discardTrailingNewline = true;\r\n                    case 10:\r\n                        lineEnd = position;\r\n                        break;\r\n                }\r\n            }\r\n            if (lineEnd === -1) {\r\n                break;\r\n            }\r\n            onLine(buffer.subarray(lineStart, lineEnd), fieldLength);\r\n            lineStart = position;\r\n            fieldLength = -1;\r\n        }\r\n        if (lineStart === bufLength) {\r\n            buffer = undefined;\r\n        }\r\n        else if (lineStart !== 0) {\r\n            buffer = buffer.subarray(lineStart);\r\n            position -= lineStart;\r\n        }\r\n    };\r\n}\r\nfunction getMessages(onId, onRetry, onMessage) {\r\n    let message = newMessage();\r\n    const decoder = new TextDecoder();\r\n    return function onLine(line, fieldLength) {\r\n        if (line.length === 0) {\r\n            onMessage === null || onMessage === void 0 ? void 0 : onMessage(message);\r\n            message = newMessage();\r\n        }\r\n        else if (fieldLength > 0) {\r\n            const field = decoder.decode(line.subarray(0, fieldLength));\r\n            const valueOffset = fieldLength + (line[fieldLength + 1] === 32 ? 2 : 1);\r\n            const value = decoder.decode(line.subarray(valueOffset));\r\n            switch (field) {\r\n                case 'data':\r\n                    message.data = message.data\r\n                        ? message.data + '\\n' + value\r\n                        : value;\r\n                    break;\r\n                case 'event':\r\n                    message.event = value;\r\n                    break;\r\n                case 'id':\r\n                    onId(message.id = value);\r\n                    break;\r\n                case 'retry':\r\n                    const retry = parseInt(value, 10);\r\n                    if (!isNaN(retry)) {\r\n                        onRetry(message.retry = retry);\r\n                    }\r\n                    break;\r\n            }\r\n        }\r\n    };\r\n}\r\nfunction concat(a, b) {\r\n    const res = new Uint8Array(a.length + b.length);\r\n    res.set(a);\r\n    res.set(b, a.length);\r\n    return res;\r\n}\r\nfunction newMessage() {\r\n    return {\r\n        data: '',\r\n        event: '',\r\n        id: '',\r\n        retry: undefined,\r\n    };\r\n}\r\n//# sourceMappingURL=parse.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@microsoft/fetch-event-source/lib/esm/parse.js\n");

/***/ })

};
;