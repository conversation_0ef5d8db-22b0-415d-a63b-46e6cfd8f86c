import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "VPS Admin Chat",
  description: "AI-powered VPS administration interface with real-time command execution and monitoring",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                try {
                  // Disable transitions during initial load
                  document.documentElement.classList.add('no-transitions');

                  // Get stored theme from localStorage
                  const stored = localStorage.getItem('vps-admin-storage');
                  let theme = 'light'; // default

                  if (stored) {
                    const parsed = JSON.parse(stored);
                    theme = parsed.state?.theme || 'light';
                  } else {
                    // Check system preference if no stored theme
                    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                      theme = 'dark';
                    }
                  }

                  // Apply theme immediately to prevent flash
                  if (theme === 'dark') {
                    document.documentElement.classList.add('dark');
                  } else {
                    document.documentElement.classList.remove('dark');
                  }

                  // Re-enable transitions after a short delay
                  setTimeout(() => {
                    document.documentElement.classList.remove('no-transitions');
                    document.body.classList.add('theme-transitions-enabled');
                  }, 100);
                } catch (e) {
                  // Fallback to light theme if anything goes wrong
                  document.documentElement.classList.remove('dark');
                  document.documentElement.classList.remove('no-transitions');
                }
              })();
            `,
          }}
        />
      </head>
      <body className="antialiased">
        {children}
      </body>
    </html>
  );
}
