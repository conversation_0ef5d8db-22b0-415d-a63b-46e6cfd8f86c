/**
 * Troubleshooting Guide Component
 * Provides step-by-step instructions for fixing common backend issues
 */

import React, { useState } from 'react';
import { ChevronDown, ChevronRight, Terminal, Server, AlertTriangle, CheckCircle } from 'lucide-react';

interface TroubleshootingStep {
  id: string;
  title: string;
  description: string;
  commands?: string[];
  notes?: string[];
}

const troubleshootingSteps: TroubleshootingStep[] = [
  {
    id: 'restart-backend-cors',
    title: 'Restart Backend with CORS Fix',
    description: 'Stop any existing backend servers and restart with updated CORS configuration',
    commands: ['restart-backend.bat'],
    notes: [
      'This script will kill existing backend processes and restart with proper CORS settings',
      'Run this from the vps-admin-frontend directory',
      'If the script fails, follow the manual steps below'
    ]
  },
  {
    id: 'check-backend-directory',
    title: 'Navigate to Backend Directory (Manual)',
    description: 'Ensure you are in the correct backend directory',
    commands: ['cd Desktop\\github-workflow\\vps-admin-frontend\\backend'],
    notes: ['Make sure this directory exists and contains main.py']
  },
  {
    id: 'check-python',
    title: 'Verify Python Installation',
    description: 'Check if Python is installed and accessible',
    commands: ['python --version', 'python3 --version'],
    notes: ['You should see Python 3.7 or higher', 'If not found, install Python from python.org']
  },
  {
    id: 'install-dependencies',
    title: 'Install Required Dependencies',
    description: 'Install all required Python packages',
    commands: ['pip install -r requirements.txt'],
    notes: ['This may take a few minutes', 'If pip is not found, try: python -m pip install -r requirements.txt']
  },
  {
    id: 'check-env-file',
    title: 'Verify Environment Configuration',
    description: 'Ensure the .env file exists and contains required variables',
    commands: ['type .env'],
    notes: [
      'The file should contain GEMINI_API_KEY and other required variables',
      'If missing, copy from .env.example or create a new one'
    ]
  },
  {
    id: 'start-backend',
    title: 'Start the Backend Server',
    description: 'Run the main backend server',
    commands: ['python main.py'],
    notes: [
      'The server should start on port 8000',
      'You should see "Setting up modular VPS AI Admin Backend..." message',
      'Keep this terminal window open while using the frontend'
    ]
  },
  {
    id: 'test-connection',
    title: 'Test Backend Connection',
    description: 'Verify the backend is responding',
    commands: ['curl http://localhost:8000/', 'powershell -Command "Invoke-WebRequest -Uri http://localhost:8000/"'],
    notes: [
      'You should get a JSON response with server information',
      'If you get an error, check the backend terminal for error messages'
    ]
  },
  {
    id: 'test-cors',
    title: 'Test CORS Configuration',
    description: 'Verify CORS is working properly',
    commands: ['Open test-cors.html in your browser'],
    notes: [
      'The test page should show successful connections',
      'If CORS errors persist, restart the backend server',
      'Make sure no other services are using port 8000'
    ]
  }
];

export const TroubleshootingGuide: React.FC = () => {
  const [expandedSteps, setExpandedSteps] = useState<Set<string>>(new Set());
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());

  const toggleStep = (stepId: string) => {
    const newExpanded = new Set(expandedSteps);
    if (newExpanded.has(stepId)) {
      newExpanded.delete(stepId);
    } else {
      newExpanded.add(stepId);
    }
    setExpandedSteps(newExpanded);
  };

  const markStepComplete = (stepId: string) => {
    const newCompleted = new Set(completedSteps);
    if (newCompleted.has(stepId)) {
      newCompleted.delete(stepId);
    } else {
      newCompleted.add(stepId);
    }
    setCompletedSteps(newCompleted);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      // Could add a toast notification here
    });
  };

  return (
    <div className="bg-theme-primary border border-theme-primary rounded-lg p-4 shadow-sm">
      <div className="flex items-center space-x-2 mb-4">
        <AlertTriangle className="w-5 h-5 text-accent-warning" />
        <h3 className="text-lg font-semibold text-theme-primary">Backend Troubleshooting Guide</h3>
      </div>

      <div className="space-y-3">
        <p className="text-sm text-theme-secondary mb-4">
          Follow these steps to diagnose and fix backend connection issues:
        </p>

        {troubleshootingSteps.map((step, index) => {
          const isExpanded = expandedSteps.has(step.id);
          const isCompleted = completedSteps.has(step.id);

          return (
            <div key={step.id} className="border border-theme-primary rounded-md">
              <button
                onClick={() => toggleStep(step.id)}
                className="w-full flex items-center justify-between p-3 text-left hover:bg-theme-tertiary transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-theme-tertiary">
                      {index + 1}.
                    </span>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        markStepComplete(step.id);
                      }}
                      className={`w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${
                        isCompleted
                          ? 'bg-accent-secondary border-accent-secondary text-white'
                          : 'border-theme-tertiary hover:border-accent-secondary'
                      }`}
                    >
                      {isCompleted && <CheckCircle className="w-3 h-3" />}
                    </button>
                  </div>
                  <span className={`font-medium ${isCompleted ? 'text-accent-secondary' : 'text-theme-primary'}`}>
                    {step.title}
                  </span>
                </div>
                {isExpanded ? (
                  <ChevronDown className="w-4 h-4 text-theme-tertiary" />
                ) : (
                  <ChevronRight className="w-4 h-4 text-theme-tertiary" />
                )}
              </button>

              {isExpanded && (
                <div className="px-3 pb-3 border-t border-theme-primary">
                  <p className="text-sm text-theme-secondary mb-3 mt-2">{step.description}</p>

                  {step.commands && step.commands.length > 0 && (
                    <div className="mb-3">
                      <div className="flex items-center space-x-2 mb-2">
                        <Terminal className="w-4 h-4 text-theme-tertiary" />
                        <span className="text-sm font-medium text-theme-primary">Commands:</span>
                      </div>
                      <div className="space-y-2">
                        {step.commands.map((command, cmdIndex) => (
                          <div key={cmdIndex} className="relative">
                            <div className="terminal-command">
                              {command}
                            </div>
                            <button
                              onClick={() => copyToClipboard(command)}
                              className="absolute top-2 right-2 text-theme-secondary hover:text-theme-primary text-xs px-2 py-1 bg-theme-secondary rounded opacity-75 hover:opacity-100 transition-opacity"
                              title="Copy to clipboard"
                            >
                              Copy
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {step.notes && step.notes.length > 0 && (
                    <div>
                      <div className="flex items-center space-x-2 mb-2">
                        <Server className="w-4 h-4 text-theme-tertiary" />
                        <span className="text-sm font-medium text-theme-primary">Notes:</span>
                      </div>
                      <ul className="space-y-1">
                        {step.notes.map((note, noteIndex) => (
                          <li key={noteIndex} className="text-xs text-theme-secondary flex items-start space-x-2">
                            <span className="text-theme-tertiary mt-1">•</span>
                            <span>{note}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>
          );
        })}

        <div className="mt-4 p-3 bg-surface-secondary border border-theme-primary rounded-md">
          <div className="flex items-center space-x-2 mb-2">
            <Server className="w-4 h-4 text-accent-primary" />
            <span className="text-accent-primary font-medium text-sm">Quick Check:</span>
          </div>
          <p className="text-theme-secondary text-xs">
            If the backend is running correctly, you should see it listening on port 8000.
            The frontend will automatically detect when the connection is restored.
          </p>
        </div>
      </div>
    </div>
  );
};

export default TroubleshootingGuide;
