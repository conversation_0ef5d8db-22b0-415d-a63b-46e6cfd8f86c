[{"C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\BackendStatus.tsx": "3", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\ChatHeader.tsx": "4", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\ChatInput.tsx": "5", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\ConfirmationButtons.tsx": "6", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\LoadingIndicator.tsx": "7", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\MarkdownRenderer.tsx": "8", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\MessageBubble.tsx": "9", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\MessageList.tsx": "10", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\ProgressIndicator.tsx": "11", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\StatsPanel.tsx": "12", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\TaskDebugPanel.tsx": "13", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\TerminalModal.tsx": "14", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\TerminalOutputWithModal.tsx": "15", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\TroubleshootingGuide.tsx": "16", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\VPSAdminChat.tsx": "17", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\constants\\index.ts": "18", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\hooks\\useCommandHistory.ts": "19", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\hooks\\useMessageHandler.ts": "20", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\hooks\\useStats.ts": "21", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\hooks\\useStreamHandler.ts": "22", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\hooks\\useTaskManager.ts": "23", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\hooks\\useTheme.ts": "24", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\hooks\\useUIState.ts": "25", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\services\\apiService.ts": "26", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\services\\streamService.ts": "27", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\store\\appStore.ts": "28", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\types\\index.ts": "29", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\utils\\index.ts": "30", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\utils\\messageRendering.ts": "31", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\utils\\terminalUtils.ts": "32", "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\providers\\StoreProvider.tsx": "33"}, {"size": 689, "mtime": 1749634800048, "results": "34", "hashOfConfig": "35"}, {"size": 392, "mtime": 1749635481939, "results": "36", "hashOfConfig": "35"}, {"size": 7714, "mtime": 1749634302969, "results": "37", "hashOfConfig": "35"}, {"size": 4538, "mtime": 1749634302984, "results": "38", "hashOfConfig": "35"}, {"size": 7001, "mtime": 1749634302999, "results": "39", "hashOfConfig": "35"}, {"size": 4379, "mtime": 1749634303016, "results": "40", "hashOfConfig": "35"}, {"size": 1418, "mtime": 1749634303029, "results": "41", "hashOfConfig": "35"}, {"size": 2153, "mtime": 1749634303044, "results": "42", "hashOfConfig": "35"}, {"size": 5848, "mtime": 1749634303059, "results": "43", "hashOfConfig": "35"}, {"size": 1713, "mtime": 1749634303077, "results": "44", "hashOfConfig": "35"}, {"size": 5949, "mtime": 1749634303095, "results": "45", "hashOfConfig": "35"}, {"size": 10665, "mtime": 1749634303113, "results": "46", "hashOfConfig": "35"}, {"size": 10044, "mtime": 1749634303128, "results": "47", "hashOfConfig": "35"}, {"size": 13186, "mtime": 1749635271874, "results": "48", "hashOfConfig": "35"}, {"size": 4792, "mtime": 1749634303162, "results": "49", "hashOfConfig": "35"}, {"size": 9696, "mtime": 1749634303175, "results": "50", "hashOfConfig": "35"}, {"size": 24880, "mtime": 1749635363899, "results": "51", "hashOfConfig": "35"}, {"size": 6272, "mtime": 1749634260721, "results": "52", "hashOfConfig": "35"}, {"size": 1535, "mtime": 1749634295889, "results": "53", "hashOfConfig": "35"}, {"size": 1273, "mtime": 1749634295904, "results": "54", "hashOfConfig": "35"}, {"size": 2679, "mtime": 1749635292285, "results": "55", "hashOfConfig": "35"}, {"size": 6659, "mtime": 1749634295932, "results": "56", "hashOfConfig": "35"}, {"size": 1531, "mtime": 1749634295953, "results": "57", "hashOfConfig": "35"}, {"size": 1371, "mtime": 1749635231756, "results": "58", "hashOfConfig": "35"}, {"size": 2137, "mtime": 1749634295980, "results": "59", "hashOfConfig": "35"}, {"size": 10309, "mtime": 1749634287822, "results": "60", "hashOfConfig": "35"}, {"size": 3459, "mtime": 1749634287839, "results": "61", "hashOfConfig": "35"}, {"size": 7529, "mtime": 1749635458581, "results": "62", "hashOfConfig": "35"}, {"size": 6452, "mtime": 1749634253983, "results": "63", "hashOfConfig": "35"}, {"size": 10337, "mtime": 1749635414793, "results": "64", "hashOfConfig": "35"}, {"size": 2800, "mtime": 1749634280900, "results": "65", "hashOfConfig": "35"}, {"size": 8158, "mtime": 1749634280917, "results": "66", "hashOfConfig": "35"}, {"size": 323, "mtime": 1749635471759, "results": "67", "hashOfConfig": "35"}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ds6kj2", {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 10, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\BackendStatus.tsx", ["167", "168", "169"], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\ChatHeader.tsx", ["170"], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\ChatInput.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\ConfirmationButtons.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\LoadingIndicator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\MarkdownRenderer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\MessageBubble.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\MessageList.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\ProgressIndicator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\StatsPanel.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\TaskDebugPanel.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\TerminalModal.tsx", ["171"], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\TerminalOutputWithModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\TroubleshootingGuide.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\VPSAdminChat.tsx", ["172", "173", "174", "175", "176", "177", "178", "179"], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\constants\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\hooks\\useCommandHistory.ts", [], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\hooks\\useMessageHandler.ts", [], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\hooks\\useStats.ts", [], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\hooks\\useStreamHandler.ts", ["180", "181"], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\hooks\\useTaskManager.ts", [], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\hooks\\useTheme.ts", [], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\hooks\\useUIState.ts", [], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\services\\apiService.ts", ["182", "183", "184", "185", "186", "187", "188", "189", "190", "191"], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\services\\streamService.ts", ["192"], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\store\\appStore.ts", ["193"], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\types\\index.ts", ["194", "195", "196"], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\utils\\index.ts", ["197", "198", "199", "200", "201", "202", "203"], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\utils\\messageRendering.ts", ["204", "205", "206"], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\utils\\terminalUtils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\github-workflow\\vps-admin-nextjs\\src\\components\\providers\\StoreProvider.tsx", [], [], {"ruleId": "207", "severity": 2, "message": "208", "line": 20, "column": 80, "nodeType": "209", "messageId": "210", "endLine": 20, "endColumn": 83, "suggestions": "211"}, {"ruleId": "212", "severity": 1, "message": "213", "line": 59, "column": 6, "nodeType": "214", "endLine": 59, "endColumn": 8, "suggestions": "215"}, {"ruleId": "212", "severity": 1, "message": "213", "line": 66, "column": 6, "nodeType": "214", "endLine": 66, "endColumn": 36, "suggestions": "216"}, {"ruleId": "217", "severity": 2, "message": "218", "line": 29, "column": 11, "nodeType": null, "messageId": "219", "endLine": 29, "endColumn": 16}, {"ruleId": "217", "severity": 2, "message": "218", "line": 22, "column": 11, "nodeType": null, "messageId": "219", "endLine": 22, "endColumn": 16}, {"ruleId": "217", "severity": 2, "message": "220", "line": 6, "column": 75, "nodeType": null, "messageId": "219", "endLine": 6, "endColumn": 86}, {"ruleId": "217", "severity": 2, "message": "218", "line": 53, "column": 5, "nodeType": null, "messageId": "219", "endLine": 53, "endColumn": 10}, {"ruleId": "212", "severity": 1, "message": "221", "line": 81, "column": 26, "nodeType": "222", "endLine": 81, "endColumn": 33}, {"ruleId": "223", "severity": 2, "message": "224", "line": 267, "column": 56, "nodeType": "225", "messageId": "226", "suggestions": "227"}, {"ruleId": "223", "severity": 2, "message": "224", "line": 267, "column": 71, "nodeType": "225", "messageId": "226", "suggestions": "228"}, {"ruleId": "207", "severity": 2, "message": "208", "line": 347, "column": 19, "nodeType": "209", "messageId": "210", "endLine": 347, "endColumn": 22, "suggestions": "229"}, {"ruleId": "223", "severity": 2, "message": "224", "line": 365, "column": 25, "nodeType": "225", "messageId": "226", "suggestions": "230"}, {"ruleId": "223", "severity": 2, "message": "224", "line": 365, "column": 40, "nodeType": "225", "messageId": "226", "suggestions": "231"}, {"ruleId": "207", "severity": 2, "message": "208", "line": 137, "column": 20, "nodeType": "209", "messageId": "210", "endLine": 137, "endColumn": 23, "suggestions": "232"}, {"ruleId": "212", "severity": 1, "message": "233", "line": 152, "column": 6, "nodeType": "214", "endLine": 152, "endColumn": 35, "suggestions": "234"}, {"ruleId": "217", "severity": 2, "message": "235", "line": 40, "column": 18, "nodeType": null, "messageId": "219", "endLine": 40, "endColumn": 19}, {"ruleId": "207", "severity": 2, "message": "208", "line": 94, "column": 48, "nodeType": "209", "messageId": "210", "endLine": 94, "endColumn": 51, "suggestions": "236"}, {"ruleId": "207", "severity": 2, "message": "208", "line": 160, "column": 100, "nodeType": "209", "messageId": "210", "endLine": 160, "endColumn": 103, "suggestions": "237"}, {"ruleId": "217", "severity": 2, "message": "235", "line": 188, "column": 18, "nodeType": null, "messageId": "219", "endLine": 188, "endColumn": 19}, {"ruleId": "217", "severity": 2, "message": "235", "line": 199, "column": 18, "nodeType": null, "messageId": "219", "endLine": 199, "endColumn": 19}, {"ruleId": "207", "severity": 2, "message": "208", "line": 246, "column": 34, "nodeType": "209", "messageId": "210", "endLine": 246, "endColumn": 37, "suggestions": "238"}, {"ruleId": "207", "severity": 2, "message": "208", "line": 286, "column": 82, "nodeType": "209", "messageId": "210", "endLine": 286, "endColumn": 85, "suggestions": "239"}, {"ruleId": "217", "severity": 2, "message": "240", "line": 316, "column": 16, "nodeType": null, "messageId": "219", "endLine": 316, "endColumn": 21}, {"ruleId": "217", "severity": 2, "message": "241", "line": 330, "column": 19, "nodeType": null, "messageId": "219", "endLine": 330, "endColumn": 20}, {"ruleId": "217", "severity": 2, "message": "241", "line": 331, "column": 26, "nodeType": null, "messageId": "219", "endLine": 331, "endColumn": 27}, {"ruleId": "207", "severity": 2, "message": "208", "line": 70, "column": 58, "nodeType": "209", "messageId": "210", "endLine": 70, "endColumn": 61, "suggestions": "242"}, {"ruleId": "217", "severity": 2, "message": "243", "line": 140, "column": 13, "nodeType": null, "messageId": "219", "endLine": 140, "endColumn": 16}, {"ruleId": "207", "severity": 2, "message": "208", "line": 40, "column": 19, "nodeType": "209", "messageId": "210", "endLine": 40, "endColumn": 22, "suggestions": "244"}, {"ruleId": "207", "severity": 2, "message": "208", "line": 66, "column": 12, "nodeType": "209", "messageId": "210", "endLine": 66, "endColumn": 15, "suggestions": "245"}, {"ruleId": "207", "severity": 2, "message": "208", "line": 105, "column": 12, "nodeType": "209", "messageId": "210", "endLine": 105, "endColumn": 15, "suggestions": "246"}, {"ruleId": "207", "severity": 2, "message": "208", "line": 60, "column": 14, "nodeType": "209", "messageId": "210", "endLine": 60, "endColumn": 17, "suggestions": "247"}, {"ruleId": "207", "severity": 2, "message": "208", "line": 218, "column": 79, "nodeType": "209", "messageId": "210", "endLine": 218, "endColumn": 82, "suggestions": "248"}, {"ruleId": "207", "severity": 2, "message": "208", "line": 264, "column": 55, "nodeType": "209", "messageId": "210", "endLine": 264, "endColumn": 58, "suggestions": "249"}, {"ruleId": "207", "severity": 2, "message": "208", "line": 300, "column": 46, "nodeType": "209", "messageId": "210", "endLine": 300, "endColumn": 49, "suggestions": "250"}, {"ruleId": "207", "severity": 2, "message": "208", "line": 300, "column": 56, "nodeType": "209", "messageId": "210", "endLine": 300, "endColumn": 59, "suggestions": "251"}, {"ruleId": "207", "severity": 2, "message": "208", "line": 312, "column": 46, "nodeType": "209", "messageId": "210", "endLine": 312, "endColumn": 49, "suggestions": "252"}, {"ruleId": "207", "severity": 2, "message": "208", "line": 312, "column": 56, "nodeType": "209", "messageId": "210", "endLine": 312, "endColumn": 59, "suggestions": "253"}, {"ruleId": "217", "severity": 2, "message": "254", "line": 9, "column": 3, "nodeType": null, "messageId": "219", "endLine": 9, "endColumn": 10}, {"ruleId": "217", "severity": 2, "message": "220", "line": 13, "column": 3, "nodeType": null, "messageId": "219", "endLine": 13, "endColumn": 14}, {"ruleId": "255", "severity": 2, "message": "256", "line": 41, "column": 7, "nodeType": "257", "messageId": "258", "endLine": 46, "endColumn": 9}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["259", "260"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'checkStatus'. Either include it or remove the dependency array.", "ArrayExpression", ["261"], ["262"], "@typescript-eslint/no-unused-vars", "'theme' is assigned a value but never used.", "unusedVar", "'ChevronDown' is defined but never used.", "The ref value 'abortControllerRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'abortControllerRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "Identifier", "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["263", "264", "265", "266"], ["267", "268", "269", "270"], ["271", "272"], ["273", "274", "275", "276"], ["277", "278", "279", "280"], ["281", "282"], "React Hook useCallback has a missing dependency: 'abortStream'. Either include it or remove the dependency array.", ["283"], "'e' is defined but never used.", ["284", "285"], ["286", "287"], ["288", "289"], ["290", "291"], "'error' is defined but never used.", "'_' is defined but never used.", ["292", "293"], "'get' is defined but never used.", ["294", "295"], ["296", "297"], ["298", "299"], ["300", "301"], ["302", "303"], ["304", "305"], ["306", "307"], ["308", "309"], ["310", "311"], ["312", "313"], "'XCircle' is defined but never used.", "react/no-children-prop", "Do not pass children as props. Instead, pass them as additional arguments to React.createElement.", "CallExpression", "pass<PERSON><PERSON><PERSON>n<PERSON><PERSON><PERSON>", {"messageId": "314", "fix": "315", "desc": "316"}, {"messageId": "317", "fix": "318", "desc": "319"}, {"desc": "320", "fix": "321"}, {"desc": "322", "fix": "323"}, {"messageId": "324", "data": "325", "fix": "326", "desc": "327"}, {"messageId": "324", "data": "328", "fix": "329", "desc": "330"}, {"messageId": "324", "data": "331", "fix": "332", "desc": "333"}, {"messageId": "324", "data": "334", "fix": "335", "desc": "336"}, {"messageId": "324", "data": "337", "fix": "338", "desc": "327"}, {"messageId": "324", "data": "339", "fix": "340", "desc": "330"}, {"messageId": "324", "data": "341", "fix": "342", "desc": "333"}, {"messageId": "324", "data": "343", "fix": "344", "desc": "336"}, {"messageId": "314", "fix": "345", "desc": "316"}, {"messageId": "317", "fix": "346", "desc": "319"}, {"messageId": "324", "data": "347", "fix": "348", "desc": "327"}, {"messageId": "324", "data": "349", "fix": "350", "desc": "330"}, {"messageId": "324", "data": "351", "fix": "352", "desc": "333"}, {"messageId": "324", "data": "353", "fix": "354", "desc": "336"}, {"messageId": "324", "data": "355", "fix": "356", "desc": "327"}, {"messageId": "324", "data": "357", "fix": "358", "desc": "330"}, {"messageId": "324", "data": "359", "fix": "360", "desc": "333"}, {"messageId": "324", "data": "361", "fix": "362", "desc": "336"}, {"messageId": "314", "fix": "363", "desc": "316"}, {"messageId": "317", "fix": "364", "desc": "319"}, {"desc": "365", "fix": "366"}, {"messageId": "314", "fix": "367", "desc": "316"}, {"messageId": "317", "fix": "368", "desc": "319"}, {"messageId": "314", "fix": "369", "desc": "316"}, {"messageId": "317", "fix": "370", "desc": "319"}, {"messageId": "314", "fix": "371", "desc": "316"}, {"messageId": "317", "fix": "372", "desc": "319"}, {"messageId": "314", "fix": "373", "desc": "316"}, {"messageId": "317", "fix": "374", "desc": "319"}, {"messageId": "314", "fix": "375", "desc": "316"}, {"messageId": "317", "fix": "376", "desc": "319"}, {"messageId": "314", "fix": "377", "desc": "316"}, {"messageId": "317", "fix": "378", "desc": "319"}, {"messageId": "314", "fix": "379", "desc": "316"}, {"messageId": "317", "fix": "380", "desc": "319"}, {"messageId": "314", "fix": "381", "desc": "316"}, {"messageId": "317", "fix": "382", "desc": "319"}, {"messageId": "314", "fix": "383", "desc": "316"}, {"messageId": "317", "fix": "384", "desc": "319"}, {"messageId": "314", "fix": "385", "desc": "316"}, {"messageId": "317", "fix": "386", "desc": "319"}, {"messageId": "314", "fix": "387", "desc": "316"}, {"messageId": "317", "fix": "388", "desc": "319"}, {"messageId": "314", "fix": "389", "desc": "316"}, {"messageId": "317", "fix": "390", "desc": "319"}, {"messageId": "314", "fix": "391", "desc": "316"}, {"messageId": "317", "fix": "392", "desc": "319"}, {"messageId": "314", "fix": "393", "desc": "316"}, {"messageId": "317", "fix": "394", "desc": "319"}, {"messageId": "314", "fix": "395", "desc": "316"}, {"messageId": "317", "fix": "396", "desc": "319"}, "suggestUnknown", {"range": "397", "text": "398"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "399", "text": "400"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [checkStatus]", {"range": "401", "text": "402"}, "Update the dependencies array to be: [autoRefresh, checkStatus, refreshInterval]", {"range": "403", "text": "404"}, "replaceWithAlt", {"alt": "405"}, {"range": "406", "text": "407"}, "Replace with `&quot;`.", {"alt": "408"}, {"range": "409", "text": "410"}, "Replace with `&ldquo;`.", {"alt": "411"}, {"range": "412", "text": "413"}, "Replace with `&#34;`.", {"alt": "414"}, {"range": "415", "text": "416"}, "Replace with `&rdquo;`.", {"alt": "405"}, {"range": "417", "text": "418"}, {"alt": "408"}, {"range": "419", "text": "420"}, {"alt": "411"}, {"range": "421", "text": "422"}, {"alt": "414"}, {"range": "423", "text": "424"}, {"range": "425", "text": "398"}, {"range": "426", "text": "400"}, {"alt": "405"}, {"range": "427", "text": "428"}, {"alt": "408"}, {"range": "429", "text": "430"}, {"alt": "411"}, {"range": "431", "text": "432"}, {"alt": "414"}, {"range": "433", "text": "434"}, {"alt": "405"}, {"range": "435", "text": "436"}, {"alt": "408"}, {"range": "437", "text": "438"}, {"alt": "411"}, {"range": "439", "text": "440"}, {"alt": "414"}, {"range": "441", "text": "442"}, {"range": "443", "text": "398"}, {"range": "444", "text": "400"}, "Update the dependencies array to be: [abortStream, onError, onMessage, onClose]", {"range": "445", "text": "446"}, {"range": "447", "text": "398"}, {"range": "448", "text": "400"}, {"range": "449", "text": "398"}, {"range": "450", "text": "400"}, {"range": "451", "text": "398"}, {"range": "452", "text": "400"}, {"range": "453", "text": "398"}, {"range": "454", "text": "400"}, {"range": "455", "text": "398"}, {"range": "456", "text": "400"}, {"range": "457", "text": "398"}, {"range": "458", "text": "400"}, {"range": "459", "text": "398"}, {"range": "460", "text": "400"}, {"range": "461", "text": "398"}, {"range": "462", "text": "400"}, {"range": "463", "text": "398"}, {"range": "464", "text": "400"}, {"range": "465", "text": "398"}, {"range": "466", "text": "400"}, {"range": "467", "text": "398"}, {"range": "468", "text": "400"}, {"range": "469", "text": "398"}, {"range": "470", "text": "400"}, {"range": "471", "text": "398"}, {"range": "472", "text": "400"}, {"range": "473", "text": "398"}, {"range": "474", "text": "400"}, {"range": "475", "text": "398"}, {"range": "476", "text": "400"}, [682, 685], "unknown", [682, 685], "never", [1909, 1911], "[checkStatus]", [2102, 2132], "[autoRefresh, checkStatus, refreshInterval]", "&quot;", [12680, 12780], "\r\n            The backend connection was lost. Check the &quot;Backend Status\" section below.\r\n          ", "&ldquo;", [12680, 12780], "\r\n            The backend connection was lost. Check the &ldquo;Backend Status\" section below.\r\n          ", "&#34;", [12680, 12780], "\r\n            The backend connection was lost. Check the &#34;Backend Status\" section below.\r\n          ", "&rdquo;", [12680, 12780], "\r\n            The backend connection was lost. Check the &rdquo;Backend Status\" section below.\r\n          ", [12680, 12780], "\r\n            The backend connection was lost. Check the \"Backend Status&quot; section below.\r\n          ", [12680, 12780], "\r\n            The backend connection was lost. Check the \"Backend Status&ldquo; section below.\r\n          ", [12680, 12780], "\r\n            The backend connection was lost. Check the \"Backend Status&#34; section below.\r\n          ", [12680, 12780], "\r\n            The backend connection was lost. Check the \"Backend Status&rdquo; section below.\r\n          ", [15506, 15509], [15506, 15509], [16343, 16446], "\r\n              Click the &quot;Backend Status\" button below to diagnose the connection issue.\r\n            ", [16343, 16446], "\r\n              Click the &ldquo;Backend Status\" button below to diagnose the connection issue.\r\n            ", [16343, 16446], "\r\n              Click the &#34;Backend Status\" button below to diagnose the connection issue.\r\n            ", [16343, 16446], "\r\n              Click the &rdquo;Backend Status\" button below to diagnose the connection issue.\r\n            ", [16343, 16446], "\r\n              Click the \"Backend Status&quot; button below to diagnose the connection issue.\r\n            ", [16343, 16446], "\r\n              Click the \"Backend Status&ldquo; button below to diagnose the connection issue.\r\n            ", [16343, 16446], "\r\n              Click the \"Backend Status&#34; button below to diagnose the connection issue.\r\n            ", [16343, 16446], "\r\n              Click the \"Backend Status&rdquo; button below to diagnose the connection issue.\r\n            ", [5551, 5554], [5551, 5554], [6130, 6159], "[abortStream, onError, onMessage, onClose]", [2896, 2899], [2896, 2899], [4856, 4859], [4856, 4859], [7114, 7117], [7114, 7117], [7934, 7937], [7934, 7937], [1966, 1969], [1966, 1969], [1325, 1328], [1325, 1328], [2055, 2058], [2055, 2058], [2790, 2793], [2790, 2793], [1887, 1890], [1887, 1890], [7004, 7007], [7004, 7007], [8679, 8682], [8679, 8682], [9724, 9727], [9724, 9727], [9734, 9737], [9734, 9737], [10045, 10048], [10045, 10048], [10055, 10058], [10055, 10058]]