/**
 * Loading indicator component for VPS Admin Chat
 */

import React from 'react';
import { Loader2 } from 'lucide-react';

interface LoadingIndicatorProps {
  message?: string;
  className?: string;
  size?: number;
  variant?: 'dots' | 'spinner';
}

const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({
  message = "AI is thinking...",
  className = "",
  size = 18,
  variant = 'dots'
}) => {
  if (variant === 'spinner') {
    return (
      <div className={`flex justify-center items-center py-3 self-center animate-fadeIn ${className}`}>
        <Loader2 size={size} className="text-sky-500 animate-spin" />
        <span className="ml-3 text-sm text-theme-secondary">{message}</span>
      </div>
    );
  }

  // Default dots variant
  return (
    <div className={`flex justify-center items-center py-3 self-center animate-fadeIn ${className}`}>
      {/* Enhanced 3-dot loader with better dark mode colors */}
      <div className="flex space-x-1">
        <div className="w-2 h-2 bg-sky-500 rounded-full animate-bounce shadow-sm"></div>
        <div className="w-2 h-2 bg-sky-500 rounded-full animate-bounce animation-delay-200 shadow-sm"></div>
        <div className="w-2 h-2 bg-sky-500 rounded-full animate-bounce animation-delay-400 shadow-sm"></div>
      </div>
      <span className="ml-3 text-sm text-theme-secondary">{message}</span>
    </div>
  );
};

export default LoadingIndicator;
